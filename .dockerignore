# =====================================================
# DOCKER IGNORE FILE
# Exclude unnecessary files from Docker build context
# =====================================================

# Git
.git
.gitignore
.gitattributes

# Maven
target/
!target/*.jar
.mvn/wrapper/maven-wrapper.jar
.mvn/wrapper/maven-wrapper.properties

# IDE
.idea/
.vscode/
*.iml
*.ipr
*.iws
.classpath
.project
.settings/
.metadata/

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Environment
.env
.env.local
.env.production
.env.staging

# Documentation
*.md
docs/

# Docker
Dockerfile*
docker-compose*
.dockerignore

# CI/CD
.github/
.gitlab-ci.yml
Jenkinsfile

# Testing
coverage/
test-results/

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Node.js (if any frontend assets)
node_modules/
npm-debug.log
yarn-error.log

# Application specific
uploads/
*.pid
.pid 