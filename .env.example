# =====================================================
# ENVIRONMENT VARIABLES TEMPLATE
# Copy this file to .env and fill in your values
# =====================================================

# ===== OPENAI CONFIGURATION =====
OPENAI_API_KEY=your-openai-api-key-here

# ===== GOOGLE OAUTH CONFIGURATION =====
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# ===== JWT CONFIGURATION =====
JWT_SECRET=mySecretKey123456789012345678901234567890RealEstateAI2024

# ===== DATABASE CONFIGURATION =====
POSTGRES_DB=realestate_membership
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password

# ===== MAIL CONFIGURATION =====
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# ===== APPLICATION CONFIGURATION =====
FRONTEND_URL=http://localhost:3000
SERVER_PORT=8080

# ===== DEPLOYMENT CONFIGURATION =====
ENVIRONMENT=development
LOG_LEVEL=INFO 