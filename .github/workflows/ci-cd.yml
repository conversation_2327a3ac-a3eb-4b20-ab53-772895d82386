# =====================================================
# GitHub Actions CI/CD Pipeline
# Real Estate Membership System
# =====================================================

name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop, dev ]
  pull_request:
    branches: [ main, develop ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  IMAGE_NAME_LOWERCASE: ${{ format('{0}', github.repository) }}

permissions:
  contents: read
  checks: write
  pull-requests: write
  security-events: write
  actions: read
  id-token: write

jobs:
  # ===== TEST JOB =====
  test:
    runs-on: ubuntu-latest
    name: Run Tests
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: password
          POSTGRES_USER: postgres
          POSTGRES_DB: realestate_membership_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up JDK 17
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Run tests
      run: ./mvnw clean test -Dspring.profiles.active=test
      env:
        SPRING_PROFILES_ACTIVE: test
        SPRING_DATASOURCE_URL: jdbc:h2:mem:testdb
        SPRING_DATASOURCE_USERNAME: sa
        SPRING_DATASOURCE_PASSWORD: 
        APP_JWT_SECRET: testSecretKeyForCIEnvironmentWithAtLeast256BitsLength12345678901234567890
        OPENAI_API_KEY: dummy-api-key
        CHATBOT_ENABLED: false

    - name: Create test report directory if not exists
      run: mkdir -p target/surefire-reports

    - name: Generate test report
      uses: dorny/test-reporter@v1
      if: success() || failure()
      with:
        name: Maven Tests
        path: target/surefire-reports/*.xml
        reporter: java-junit

    - name: Upload coverage reports to Codecov
      uses: codecov/codecov-action@v3
      if: success()
      with:
        file: target/site/jacoco/jacoco.xml
        fail_ci_if_error: false

  # ===== BUILD JOB =====
  build:
    runs-on: ubuntu-latest
    needs: test
    name: Build Application
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up JDK 17
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Build application
      run: ./mvnw clean package -DskipTests

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: jar-artifact
        path: target/*.jar

  # ===== DOCKER BUILD & PUSH =====
  docker:
    runs-on: ubuntu-latest
    needs: build
    name: Build and Push Docker Image
    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download JAR artifact
      uses: actions/download-artifact@v4
      with:
        name: jar-artifact
        path: target

    - name: List files in target directory
      run: ls -la target/

    - name: Set lowercase image name
      run: echo "LOWERCASE_REPO=$(echo ${{ github.repository }} | tr '[:upper:]' '[:lower:]')" >> $GITHUB_ENV

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.LOWERCASE_REPO }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # ===== SECURITY SCAN =====
  security:
    runs-on: ubuntu-latest
    needs: docker
    name: Security Scan
    permissions:
      contents: read
      security-events: write
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set lowercase image name
      run: echo "LOWERCASE_REPO=$(echo ${{ github.repository }} | tr '[:upper:]' '[:lower:]')" >> $GITHUB_ENV

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.REGISTRY }}/${{ env.LOWERCASE_REPO }}:latest
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v3
      with:
        sarif_file: 'trivy-results.sarif'
        category: 'trivy'

  # ===== SUMMARY =====
  summary:
    runs-on: ubuntu-latest
    needs: [test, build, docker, security]
    if: always()
    name: Pipeline Summary
    
    steps:
    - name: Pipeline status
      run: |
        echo "CI/CD Pipeline completed"
        echo "Tests, build, Docker image creation and security scan completed"
        echo "Docker image available at: ${{ env.REGISTRY }}/${{ github.repository }}:latest"
      if: always() 