# Tài liệu API Hệ thống Quản lý Thành viên Bất động sản

## Giới thiệu

Đây là tài liệu mô tả các API của Hệ thống Quản lý Thành viên Bất động sản. Hệ thống được phát triển bằng Spring Boot và cung cấp các API cho việc quản lý người dùng, thành viê<PERSON>, b<PERSON><PERSON> đ<PERSON><PERSON> sả<PERSON>, danh mụ<PERSON>, ảnh bất động sản, và hỗ trợ trò chuyện AI.

## 🔐 Xác thực

### 1. Đăng ký người dùng

- **URL**: `/auth/register`
- **Phương thức**: POST
- **Mô tả**: Đăng ký người dùng mới với xác minh email
- **Body**:
  ```json
  {
    "username": "string",
    "email": "string",
    "password": "string",
    "fullName": "string"
  }
  ```
- **<PERSON><PERSON><PERSON> hồi**:
  ```json
  {
    "token": "string",
    "user": {
      "id": "number",
      "username": "string",
      "email": "string",
      "fullName": "string",
      "roles": ["string"]
    }
  }
  ```

### 2. Đăng nhập

- **URL**: `/auth/login`
- **Phương thức**: POST
- **Mô tả**: Đăng nhập người dùng (không hiển thị user ID để bảo mật)
- **Body**:
  ```json
  {
    "username": "string",
    "password": "string"
  }
  ```
- **Phản hồi**:
  ```json
  {
    "token": "string",
    "user": {
      "username": "string",
      "email": "string",
      "fullName": "string",
      "roles": ["string"]
    }
  }
  ```

## Xác thực OAuth

### 1. Xác thực thành công

- **URL**: `/oauth/login/success`
- **Phương thức**: GET
- **Mô tả**: Callback khi xác thực OAuth thành công

### 2. Xác thực thất bại

- **URL**: `/oauth/login/failure`
- **Phương thức**: GET
- **Mô tả**: Callback khi xác thực OAuth thất bại

### 3. Liên kết tài khoản

- **URL**: `/oauth/link-account`
- **Phương thức**: POST
- **Mô tả**: Liên kết tài khoản OAuth với tài khoản hiện có
- **Body**:
  ```json
  {
    "email": "string",
    "password": "string"
  }
  ```

### 4. Thông tin người dùng OAuth

- **URL**: `/oauth/user-info`
- **Phương thức**: GET
- **Mô tả**: Lấy thông tin người dùng OAuth

### 5. Ngắt kết nối OAuth

- **URL**: `/oauth/disconnect`
- **Phương thức**: POST
- **Mô tả**: Ngắt kết nối tài khoản OAuth
- **Tham số**:
  - `userId`: ID của người dùng

## Quản lý thành viên

### 1. Lấy tất cả gói thành viên

- **URL**: `/memberships`
- **Phương thức**: GET
- **Mô tả**: Lấy tất cả các gói thành viên đang hoạt động

### 2. Lấy thông tin gói thành viên theo ID

- **URL**: `/memberships/{id}`
- **Phương thức**: GET
- **Mô tả**: Lấy thông tin gói thành viên theo ID
- **Tham số đường dẫn**:
  - `id`: ID của gói thành viên

### 3. Đăng ký gói thành viên

- **URL**: `/memberships/{membershipId}/subscribe`
- **Phương thức**: POST
- **Mô tả**: Đăng ký gói thành viên
- **Tham số đường dẫn**:
  - `membershipId`: ID của gói thành viên
- **Yêu cầu**: Xác thực JWT

### 4. Lấy gói thành viên đang hoạt động của người dùng

- **URL**: `/memberships/my-membership`
- **Phương thức**: GET
- **Mô tả**: Lấy gói thành viên đang hoạt động của người dùng hiện tại
- **Yêu cầu**: Xác thực JWT

### 5. Lấy lịch sử thành viên

- **URL**: `/memberships/my-history`
- **Phương thức**: GET
- **Mô tả**: Lấy lịch sử thành viên của người dùng hiện tại
- **Yêu cầu**: Xác thực JWT

## 🏠 Quản lý bất động sản (SEO-friendly với slug và tìm kiếm nâng cao)

### 1. Lấy tất cả bất động sản đã được phê duyệt

- **URL**: `/properties`
- **Phương thức**: GET
- **Mô tả**: Lấy tất cả bất động sản đã được phê duyệt
- **Tham số truy vấn**:
  - `page`: Số trang (mặc định: 0)
  - `size`: Kích thước trang (mặc định: 10)
  - `sortBy`: Sắp xếp theo trường (mặc định: createdAt)
  - `sortDir`: Hướng sắp xếp (mặc định: desc)

### 2. Tìm kiếm bất động sản

- **URL**: `/properties/search`
- **Phương thức**: GET
- **Mô tả**: Tìm kiếm bất động sản với các bộ lọc
- **Tham số truy vấn**:
  - `city`: Thành phố
  - `district`: Quận/huyện
  - `propertyType`: Loại bất động sản (APARTMENT, HOUSE, LAND, COMMERCIAL)
  - `listingType`: Loại danh sách (SALE, RENT)
  - `minPrice`: Giá tối thiểu
  - `maxPrice`: Giá tối đa
  - `categoryId`: ID danh mục
  - `page`: Số trang (mặc định: 0)
  - `size`: Kích thước trang (mặc định: 10)

### 3. Lấy bất động sản nổi bật

- **URL**: `/properties/featured`
- **Phương thức**: GET
- **Mô tả**: Lấy danh sách bất động sản nổi bật
- **Tham số truy vấn**:
  - `limit`: Số lượng tối đa (mặc định: 10)

### 4. Lấy thông tin bất động sản theo ID

- **URL**: `/properties/{id}`
- **Phương thức**: GET
- **Mô tả**: Lấy thông tin bất động sản theo ID
- **Tham số đường dẫn**:
  - `id`: ID của bất động sản

### 5. Tạo bất động sản mới

- **URL**: `/properties`
- **Phương thức**: POST
- **Mô tả**: Tạo bất động sản mới
- **Yêu cầu**: Xác thực JWT
- **Body**: PropertyRequest
  ```json
  {
    "title": "string",
    "description": "string",
    "price": "number",
    "address": "string",
    "city": "string",
    "district": "string",
    "propertyType": "APARTMENT|HOUSE|LAND|COMMERCIAL",
    "listingType": "SALE|RENT",
    "bedrooms": "number",
    "bathrooms": "number",
    "area": "number",
    "categoryId": "number",
    "featured": "boolean"
  }
  ```

### 6. Cập nhật bất động sản

- **URL**: `/properties/{id}`
- **Phương thức**: PUT
- **Mô tả**: Cập nhật thông tin bất động sản
- **Tham số đường dẫn**:
  - `id`: ID của bất động sản
- **Yêu cầu**: Xác thực JWT
- **Body**: PropertyRequest

### 7. Xóa bất động sản

- **URL**: `/properties/{id}`
- **Phương thức**: DELETE
- **Mô tả**: Xóa bất động sản
- **Tham số đường dẫn**:
  - `id`: ID của bất động sản
- **Yêu cầu**: Xác thực JWT

### 8. Lấy bất động sản của người dùng hiện tại

- **URL**: `/properties/my-properties`
- **Phương thức**: GET
- **Mô tả**: Lấy danh sách bất động sản của người dùng hiện tại
- **Tham số truy vấn**:
  - `page`: Số trang (mặc định: 0)
  - `size`: Kích thước trang (mặc định: 10)
- **Yêu cầu**: Xác thực JWT

## 📸 Quản lý ảnh bất động sản

### 1. Tải lên ảnh bất động sản

- **URL**: `/properties/{propertyId}/images`
- **Phương thức**: POST
- **Content-Type**: multipart/form-data
- **Mô tả**: Tải lên ảnh cho bất động sản
- **Tham số đường dẫn**:
  - `propertyId`: ID của bất động sản
- **Yêu cầu**: Xác thực JWT (chỉ chủ sở hữu bất động sản)
- **Form Data**:
  - `file`: File ảnh (MultipartFile)
  - `altText`: Text mô tả ảnh (tùy chọn)
  - `isPrimary`: Có phải ảnh chính hay không (mặc định: false)
  - `sortOrder`: Thứ tự sắp xếp (mặc định: 0)

### 2. Lấy danh sách ảnh bất động sản

- **URL**: `/properties/{propertyId}/images`
- **Phương thức**: GET
- **Mô tả**: Lấy tất cả ảnh của bất động sản
- **Tham số đường dẫn**:
  - `propertyId`: ID của bất động sản
- **Phản hồi**:
  ```json
  [
    {
      "id": "number",
      "imageUrl": "string",
      "altText": "string",
      "isPrimary": "boolean",
      "sortOrder": "number"
    }
  ]
  ```

### 3. Xóa ảnh bất động sản

- **URL**: `/properties/{propertyId}/images/{imageId}`
- **Phương thức**: DELETE
- **Mô tả**: Xóa ảnh của bất động sản
- **Tham số đường dẫn**:
  - `propertyId`: ID của bất động sản
  - `imageId`: ID của ảnh
- **Yêu cầu**: Xác thực JWT (chỉ chủ sở hữu bất động sản)

## Quản lý danh mục

### 1. Lấy tất cả danh mục đang hoạt động

- **URL**: `/categories`
- **Phương thức**: GET
- **Mô tả**: Lấy tất cả danh mục đang hoạt động

### 2. Lấy thông tin danh mục theo ID

- **URL**: `/categories/{id}`
- **Phương thức**: GET
- **Mô tả**: Lấy thông tin danh mục theo ID
- **Tham số đường dẫn**:
  - `id`: ID của danh mục

### 3. Tạo danh mục mới

- **URL**: `/categories`
- **Phương thức**: POST
- **Mô tả**: Tạo danh mục mới
- **Yêu cầu**: Xác thực JWT và vai trò ADMIN
- **Body**: Category

### 4. Cập nhật danh mục

- **URL**: `/categories/{id}`
- **Phương thức**: PUT
- **Mô tả**: Cập nhật thông tin danh mục
- **Tham số đường dẫn**:
  - `id`: ID của danh mục
- **Yêu cầu**: Xác thực JWT và vai trò ADMIN
- **Body**: Category

### 5. Xóa danh mục

- **URL**: `/categories/{id}`
- **Phương thức**: DELETE
- **Mô tả**: Xóa danh mục
- **Tham số đường dẫn**:
  - `id`: ID của danh mục
- **Yêu cầu**: Xác thực JWT và vai trò ADMIN

## 🤖 Chatbot AI

### 1. Gửi tin nhắn đến chatbot

- **URL**: `/chatbot/chat`
- **Phương thức**: POST
- **Mô tả**: Gửi tin nhắn đến chatbot AI
- **Yêu cầu**: Xác thực JWT và vai trò USER hoặc ADMIN
- **Body**:
  ```json
  {
    "message": "string",
    "sessionId": "number"
  }
  ```
- **Phản hồi**:
  ```json
  {
    "success": true,
    "response": "string",
    "timestamp": "number"
  }
  ```

### 2. Tạo phiên trò chuyện mới

- **URL**: `/chatbot/sessions`
- **Phương thức**: POST
- **Mô tả**: Tạo phiên trò chuyện mới
- **Yêu cầu**: Xác thực JWT và vai trò USER hoặc ADMIN
- **Body**:
  ```json
  {
    "title": "string"
  }
  ```

### 3. Lấy danh sách phiên trò chuyện

- **URL**: `/chatbot/sessions`
- **Phương thức**: GET
- **Mô tả**: Lấy danh sách phiên trò chuyện của người dùng
- **Yêu cầu**: Xác thực JWT và vai trò USER hoặc ADMIN

### 4. Lấy lịch sử trò chuyện

- **URL**: `/chatbot/sessions/{sessionId}/history`
- **Phương thức**: GET
- **Mô tả**: Lấy lịch sử trò chuyện của một phiên
- **Tham số đường dẫn**:
  - `sessionId`: ID của phiên trò chuyện
- **Tham số truy vấn**:
  - `limit`: Số lượng tin nhắn tối đa (mặc định: 50)
- **Yêu cầu**: Xác thực JWT và vai trò USER hoặc ADMIN

### 5. Tìm kiếm nhanh bằng AI (Nâng cao với Property IDs)

- **URL**: `/chatbot/quick-search`
- **Phương thức**: POST
- **Mô tả**: Tìm kiếm nhanh bất động sản bằng AI với khả năng trả về ID bất động sản cụ thể
- **Yêu cầu**: Xác thực JWT và vai trò USER hoặc ADMIN
- **Body**:
  ```json
  {
    "query": "string"
  }
  ```
- **Phản hồi**:
  ```json
  {
    "success": true,
    "query": "string",
    "response": "string",
    "hasPropertyResults": "boolean",
    "propertyCount": "number",
    "properties": [
      {
        "id": "number",
        "title": "string",
        "price": "number",
        "location": "string"
      }
    ]
  }
  ```

## 👑 Admin Management

### 1. Quản lý người dùng

- **Thống kê tổng quan**: `GET /admin/stats` (Admin only)
- **Danh sách người dùng**: `GET /admin/users` (Admin only)
- **Chi tiết người dùng**: `GET /admin/users/{userId}` (Admin only)
- **Cập nhật trạng thái**: `PUT /admin/users/{userId}/status` (Admin only)
- **Khóa/mở khóa tài khoản**: `PUT /admin/users/{userId}/ban` (Admin only)

### 2. Quản lý thanh toán

- **Tất cả thanh toán**: `GET /payments/admin/all` (Admin only)
- **Hoàn tiền**: `POST /payments/admin/{paymentId}/refund` (Admin only)
- **Thống kê thanh toán**: `GET /admin/payments/stats` (Admin only)

### 3. Quản lý thông báo

- **Gửi thông báo hệ thống**: `POST /admin/notifications/broadcast` (Admin only)
- **Thống kê thông báo**: `GET /admin/notifications/stats` (Admin only)

### 4. Quản lý bất động sản

- **Duyệt bất động sản**: `PUT /admin/properties/{propertyId}/approve` (Admin only)
- **Từ chối bất động sản**: `PUT /admin/properties/{propertyId}/reject` (Admin only)
- **Bất động sản chờ duyệt**: `GET /admin/properties/pending` (Admin only)

## 🔒 Lưu ý bảo mật

- **JWT Token**: Các API có gắn thẻ `security = @SecurityRequirement(name = "bearerAuth")` yêu cầu xác thực JWT.
- **Vai trò ADMIN**: Các API có gắn `@PreAuthorize("hasRole('ADMIN')")` yêu cầu người dùng có vai trò ADMIN.
- **Vai trò USER hoặc ADMIN**: Các API có gắn `@PreAuthorize("hasRole('USER') or hasRole('ADMIN')")` yêu cầu người dùng có vai trò USER hoặc ADMIN.
- **Quyền sở hữu**: API quản lý ảnh bất động sản yêu cầu xác minh quyền sở hữu bất động sản.

## Endpoints công khai (Không cần xác thực)

- `GET /properties/**` - Xem bất động sản
- `GET /categories/**` - Xem danh mục
- `GET /memberships/**` - Xem gói thành viên
- `/auth/**` - Xác thực
- `/oauth/**` - OAuth
- `/uploads/**` - File tĩnh
- `GET /payments/methods` - Phương thức thanh toán
- `GET /payments/exchange-rate` - Tỷ giá hối đoái
- `GET /payments/vnpay/callback` - VNPay callback
- `POST /payments/vnpay/webhook` - VNPay webhook
- `POST /payments/momo/callback` - MoMo callback
- `POST /payments/momo/webhook` - MoMo webhook
- `POST /payments/stripe/webhook` - Stripe webhook
- Swagger UI và API docs

## 📊 Mô hình dữ liệu chính

### User (Người dùng)
- id: Long
- username: String
- email: String
- password: String
- fullName: String
- roles: Set<Role>

### Membership (Gói thành viên)
- id: Long
- name: String
- description: String
- price: BigDecimal
- durationDays: Integer
- features: List<String>
- active: Boolean

### UserMembership (Đăng ký thành viên)
- id: Long
- user: User
- membership: Membership
- startDate: LocalDateTime
- endDate: LocalDateTime
- active: Boolean

### Property (Bất động sản)
- id: Long
- title: String
- description: String
- price: BigDecimal
- address: String
- city: String
- district: String
- propertyType: PropertyType (APARTMENT, HOUSE, LAND, COMMERCIAL)
- listingType: ListingType (SALE, RENT)
- bedrooms: Integer
- bathrooms: Integer
- area: Double
- featured: Boolean
- approved: Boolean
- owner: User
- category: Category
- createdAt: LocalDateTime
- updatedAt: LocalDateTime

### PropertyImage (Ảnh bất động sản)
- id: Long
- property: Property
- imageUrl: String
- altText: String
- isPrimary: Boolean
- sortOrder: Integer
- createdAt: LocalDateTime

### Category (Danh mục)
- id: Long
- name: String
- description: String
- active: Boolean

### ChatSession (Phiên trò chuyện)
- id: Long
- user: User
- title: String
- messageCount: Integer
- lastActivityAt: LocalDateTime
- createdAt: LocalDateTime

### Payment (Thanh toán)
- id: Long
- user: User
- membershipId: Long
- amount: BigDecimal
- paymentMethod: PaymentMethod (VNPAY, MOMO, STRIPE, BANK_TRANSFER, CASH, WALLET)
- status: PaymentStatus (PENDING, COMPLETED, FAILED, CANCELLED, REFUNDED)
- orderId: String
- gatewayTransactionId: String
- gatewayResponseCode: String
- gatewayMessage: String
- bankCode: String
- description: String
- paymentDate: LocalDateTime
- completedAt: LocalDateTime
- createdAt: LocalDateTime

### Notification (Thông báo)
- id: Long
- user: User
- title: String
- message: String
- type: NotificationType (MEMBERSHIP_EXPIRY_WARNING, PROPERTY_APPROVED, PAYMENT_SUCCESS, etc.)
- priority: NotificationPriority (LOW, NORMAL, HIGH, URGENT)
- status: NotificationStatus (UNREAD, READ, ARCHIVED, DELETED)
- sendEmail: Boolean
- sendPush: Boolean
- sendSms: Boolean
- emailSent: Boolean
- pushSent: Boolean
- smsSent: Boolean
- propertyId: Long
- membershipId: Long
- paymentId: Long
- actionUrl: String
- actionText: String
- scheduledAt: LocalDateTime
- sentAt: LocalDateTime
- readAt: LocalDateTime
- expiresAt: LocalDateTime
- createdAt: LocalDateTime

## 💳 Payment Gateway (VNPay, MoMo, Stripe)

### 1. Tạo thanh toán chung

- **URL**: `/payments/create`
- **Phương thức**: POST
- **Mô tả**: Tạo thanh toán mới (gateway tự động)
- **Yêu cầu**: Xác thực JWT và vai trò USER hoặc ADMIN
- **Body**:
  ```json
  {
    "membershipId": "number",
    "paymentMethod": "VNPAY|MOMO|STRIPE",
    "amount": "number",
    "description": "string",
    "bankCode": "string",
    "returnUrl": "string"
  }
  ```

### 2. Tạo thanh toán VNPay

- **URL**: `/payments/vnpay/create`
- **Phương thức**: POST
- **Mô tả**: Tạo URL thanh toán VNPay
- **Yêu cầu**: Xác thực JWT
- **Body**: PaymentRequest
- **Phản hồi**:
  ```json
  {
    "paymentId": "number",
    "paymentUrl": "string",
    "orderId": "string",
    "amount": "number",
    "gateway": "VNPAY",
    "expires": "number"
  }
  ```

### 3. Tạo thanh toán MoMo

- **URL**: `/payments/momo/create`
- **Phương thức**: POST
- **Mô tả**: Tạo URL thanh toán MoMo
- **Yêu cầu**: Xác thực JWT
- **Body**: PaymentRequest với requestType (captureWallet/payWithATM)
- **Phản hồi**:
  ```json
  {
    "paymentId": "number",
    "payUrl": "string",
    "orderId": "string",
    "amount": "number",
    "gateway": "MOMO",
    "deeplink": "string"
  }
  ```

### 4. Tạo Stripe PaymentIntent

- **URL**: `/payments/stripe/create-intent`
- **Phương thức**: POST
- **Mô tả**: Tạo Stripe PaymentIntent cho custom checkout
- **Yêu cầu**: Xác thực JWT
- **Body**: PaymentRequest
- **Phản hồi**:
  ```json
  {
    "paymentId": "number",
    "clientSecret": "string",
    "paymentIntentId": "string",
    "amountUSD": "number",
    "currency": "usd",
    "publishableKey": "string"
  }
  ```

### 5. Tạo Stripe Checkout Session

- **URL**: `/payments/stripe/create-checkout`
- **Phương thức**: POST
- **Mô tả**: Tạo Stripe Checkout Session (hosted)
- **Yêu cầu**: Xác thực JWT
- **Body**: PaymentRequest
- **Phản hồi**:
  ```json
  {
    "paymentId": "number",
    "sessionId": "string",
    "checkoutUrl": "string",
    "amountUSD": "number"
  }
  ```

### 6. Callback và Webhook

- **VNPay Callback**: `GET /payments/vnpay/callback` (Public)
- **VNPay Webhook**: `POST /payments/vnpay/webhook` (Public)
- **MoMo Callback**: `POST /payments/momo/callback` (Public)
- **MoMo Webhook**: `POST /payments/momo/webhook` (Public)
- **Stripe Webhook**: `POST /payments/stripe/webhook` (Public)

### 7. Quản lý thanh toán

- **Kiểm tra trạng thái**: `GET /payments/{paymentId}/status`
- **Xác minh thanh toán**: `POST /payments/{paymentId}/verify`
- **Hủy thanh toán**: `POST /payments/{paymentId}/cancel`
- **Lịch sử thanh toán**: `GET /payments/my-payments`
- **Thanh toán theo membership**: `GET /payments/membership/{membershipId}`

### 8. Cấu hình thanh toán

- **Phương thức thanh toán**: `GET /payments/methods`
- **Tỷ giá hối đoái**: `GET /payments/exchange-rate`

### 9. Admin Payment Management

- **Tất cả thanh toán**: `GET /payments/admin/all` (Admin only)
- **Hoàn tiền**: `POST /payments/admin/{paymentId}/refund` (Admin only)

## 🔔 Notification System

### 1. Lấy thông báo của user

- **URL**: `/notifications`
- **Phương thức**: GET
- **Mô tả**: Lấy danh sách thông báo của user hiện tại
- **Yêu cầu**: Xác thực JWT
- **Tham số truy vấn**:
  - `page`: Số trang (mặc định: 0)
  - `size`: Kích thước trang (mặc định: 20)
  - `sortBy`: Sắp xếp theo (mặc định: createdAt)
  - `sortDir`: Hướng sắp xếp (mặc định: desc)
- **Phản hồi**:
  ```json
  {
    "content": [
      {
        "id": "number",
        "title": "string",
        "message": "string",
        "type": "string",
        "priority": "string",
        "status": "string",
        "read": "boolean",
        "actionUrl": "string",
        "actionText": "string",
        "displayType": "string",
        "typeIcon": "string",
        "priorityColor": "string",
        "timeAgo": "string",
        "createdAt": "datetime"
      }
    ],
    "totalElements": "number",
    "totalPages": "number"
  }
  ```

### 2. Lấy thông báo chưa đọc

- **URL**: `/notifications/unread`
- **Phương thức**: GET
- **Mô tả**: Lấy tất cả thông báo chưa đọc
- **Yêu cầu**: Xác thực JWT

### 3. Đếm thông báo chưa đọc

- **URL**: `/notifications/unread/count`
- **Phương thức**: GET
- **Mô tả**: Đếm số lượng thông báo chưa đọc
- **Yêu cầu**: Xác thực JWT
- **Phản hồi**:
  ```json
  {
    "unreadCount": "number",
    "hasUnread": "boolean"
  }
  ```

### 4. Lấy thông báo theo ID

- **URL**: `/notifications/{id}`
- **Phương thức**: GET
- **Mô tả**: Lấy chi tiết thông báo theo ID
- **Yêu cầu**: Xác thực JWT

### 5. Đánh dấu đã đọc

- **URL**: `/notifications/{id}/read`
- **Phương thức**: PUT
- **Mô tả**: Đánh dấu thông báo đã đọc
- **Yêu cầu**: Xác thực JWT

### 6. Đánh dấu tất cả đã đọc

- **URL**: `/notifications/read-all`
- **Phương thức**: PUT
- **Mô tả**: Đánh dấu tất cả thông báo đã đọc
- **Yêu cầu**: Xác thực JWT

### 7. Lưu trữ thông báo

- **URL**: `/notifications/{id}/archive`
- **Phương thức**: PUT
- **Mô tả**: Lưu trữ thông báo
- **Yêu cầu**: Xác thực JWT

### 8. Xóa thông báo

- **URL**: `/notifications/{id}`
- **Phương thức**: DELETE
- **Mô tả**: Xóa thông báo
- **Yêu cầu**: Xác thực JWT

## 🚀 Tính năng nổi bật

1. **SEO-friendly URLs**: Hỗ trợ slug cho bất động sản
2. **Tìm kiếm nâng cao**: Bộ lọc đa dạng cho bất động sản
3. **AI Chatbot thông minh**: Hỗ trợ tiếng Việt và trả về property IDs cụ thể
4. **Quản lý ảnh**: Upload và quản lý ảnh bất động sản với metadata
5. **OAuth Google**: Đăng nhập nhanh chóng với Google
6. **Payment Gateway**: Tích hợp VNPay, MoMo, Stripe với webhook tự động
7. **Notification System**: Thông báo real-time với email, push, SMS
8. **Phân quyền chi tiết**: Bảo mật đa lớp cho các API
9. **Pagination**: Hỗ trợ phân trang cho tất cả danh sách 