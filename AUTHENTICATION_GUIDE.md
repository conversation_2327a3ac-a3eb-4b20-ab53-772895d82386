# 🔐 Authentication System - Real Estate Membership

## 🌟 Tổng <PERSON>uan T<PERSON>h <PERSON> thống authentication hoàn chỉnh với email verification, password reset, và OAuth Google integration.

## 📋 Danh Sách API Endpoints

### 🔓 **Authentication Cơ Bản**

#### 1. Đ<PERSON><PERSON>
```bash
POST /api/v1/auth/register
Content-Type: application/json

{
  "username": "string",
  "email": "string", 
  "password": "string",
  "firstName": "string",
  "lastName": "string",
  "phoneNumber": "string" (optional)
}
```

**Response:**
```json
{
  "token": null,
  "type": "Bearer",
  "username": "string",
  "email": "string",
  "role": "USER",
  "message": "Tài khoản đã được tạo. Vui lòng kiểm tra email để xác thực tài khoản."
}
```

#### 2. <PERSON><PERSON><PERSON> (<PERSON><PERSON>)
```bash
POST /api/v1/auth/login
Content-Type: application/json

# Option 1: usernameOrEmail
{
  "usernameOrEmail": "string",
  "password": "string"
}

# Option 2: username riêng biệt
{
  "username": "string", 
  "password": "string"
}

# Option 3: email riêng biệt
{
  "email": "string",
  "password": "string"
}
```

**Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiJ9...",
  "type": "Bearer",
  "username": "string",
  "email": "string", 
  "role": "USER|ADMIN",
  "message": "Đăng nhập thành công!"
}
```

### 📧 **Email Verification**

#### 3. Xác Thực Email
```bash
GET /api/v1/auth/verify-email?token={token}&email={email}
```

**Response:**
```json
{
  "success": true,
  "message": "Email đã được xác thực thành công! Bạn có thể đăng nhập ngay bây giờ.",
  "redirectUrl": "/login"
}
```

#### 4. Gửi Lại Email Xác Thực
```bash
POST /api/v1/auth/resend-verification
Content-Type: application/json

{
  "email": "string"
}
```

### 🔑 **Password Reset**

#### 5. Quên Mật Khẩu
```bash
POST /api/v1/auth/forgot-password
Content-Type: application/json

{
  "email": "string"
}
```

#### 6. Đặt Lại Mật Khẩu
```bash
POST /api/v1/auth/reset-password
Content-Type: application/json

{
  "email": "string",
  "token": "string",
  "newPassword": "string",
  "confirmPassword": "string"
}
```

### 🔍 **Utility Endpoints**

#### 7. Kiểm Tra Email
```bash
GET /api/v1/auth/check-email?email={email}
```

**Response:**
```json
{
  "exists": true,
  "message": "Email đã được sử dụng"
}
```

#### 8. Kiểm Tra Username
```bash
GET /api/v1/auth/check-username?username={username}
```

### 🔗 **OAuth Google Integration**

#### 9. Google OAuth Login
```bash
GET /oauth2/authorization/google
# Redirect to Google OAuth
```

#### 10. Link OAuth Account
```bash
POST /api/v1/oauth/link-account
Content-Type: application/json

{
  "email": "string",
  "password": "string"
}
```

#### 11. Disconnect OAuth
```bash
DELETE /api/v1/oauth/disconnect
Authorization: Bearer {token}
```

## 🎯 Workflow Tích Hợp

### 📝 **Quy Trình Đăng Ký**

1. **User đăng ký** → `POST /auth/register`
2. **Hệ thống tạo account** với `emailVerified = false`
3. **Gửi email xác thực** với token (hết hạn 24h)
4. **User click link** → `GET /auth/verify-email`
5. **Account được kích hoạt** → Gửi welcome email + notification
6. **User có thể đăng nhập**

### 🔓 **Quy Trình Đăng Nhập**

1. **Kiểm tra email verified** trước khi authenticate
2. **Nếu chưa verify** → Trả lỗi với hướng dẫn
3. **Nếu đã verify** → Generate JWT token
4. **Update last login time**

### 🔑 **Quy Trình Reset Password**

1. **User quên mật khẩu** → `POST /auth/forgot-password`
2. **Hệ thống gửi email** với reset token (hết hạn 1h)
3. **User click link** → Frontend form với token
4. **Submit new password** → `POST /auth/reset-password`
5. **Password updated** → Gửi confirmation email

### 🔗 **OAuth Google Flow**

1. **User click "Login with Google"**
2. **Redirect to Google OAuth**
3. **User authorize**
4. **Google callback** → Auto create/link account
5. **Auto verify email** cho OAuth users
6. **Return JWT token**

## 🎨 Email Templates

### 📧 **Email Verification**
- **Subject:** 🔐 Xác thực tài khoản - Real Estate System
- **Content:** HTML template với brand colors
- **CTA Button:** "Xác thực Email"
- **Expiry:** 24 giờ

### 🔑 **Password Reset**
- **Subject:** 🔑 Đặt lại mật khẩu - Real Estate System  
- **Content:** Security warnings + instructions
- **CTA Button:** "Đặt lại mật khẩu"
- **Expiry:** 1 giờ

### 🎉 **Welcome Email**
- **Subject:** 🎉 Chào mừng đến với Real Estate System!
- **Content:** Feature overview + getting started
- **CTA Button:** "Bắt đầu sử dụng"

## ⚙️ Configuration

### 📧 **Email Settings**
```properties
# SMTP Configuration
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=your-app-password

# Email Constants (Customizable)
email.verification.expiry-hours=24
email.password-reset.expiry-hours=1
email.verification.subject=🔐 Xác thực tài khoản - Real Estate System
email.reset.subject=🔑 Đặt lại mật khẩu - Real Estate System
email.welcome.subject=🎉 Chào mừng đến với Real Estate System!
email.support.email=<EMAIL>
email.support.phone=1900-1234
email.company.name=Real Estate System
```

### 🔗 **OAuth Google**
```properties
spring.security.oauth2.client.registration.google.client-id=your-google-client-id
spring.security.oauth2.client.registration.google.client-secret=your-google-client-secret
spring.security.oauth2.client.registration.google.scope=openid,email,profile
```

### 🔐 **JWT Configuration**
```properties
app.jwt.secret=your-256-bit-secret-key
app.jwt.expiration=86400000 # 24 hours
app.jwt.refresh-expiration=********* # 7 days
```

## 🛡️ Security Features

### ✅ **Implemented**
- ✅ Email verification required
- ✅ Secure password reset with tokens
- ✅ JWT authentication
- ✅ OAuth Google integration
- ✅ CSRF protection disabled (API-only)
- ✅ CORS configuration
- ✅ Password encryption (BCrypt)
- ✅ Token expiration handling
- ✅ Rate limiting ready (configurable)

### 🔒 **Security Best Practices**
- **No password exposure** in responses
- **Token-based verification** instead of direct links
- **Time-limited tokens** with expiry
- **Secure random token generation**
- **Input validation** on all endpoints
- **SQL injection protection** via JPA
- **No user ID exposure** in public APIs

## 🧪 Testing

### 🧪 **Admin Account (Pre-created)**
```json
{
  "username": "admin",
  "email": "<EMAIL>", 
  "password": "admin123",
  "role": "ADMIN",
  "emailVerified": true
}
```

### 🔍 **Test Scenarios**

1. **Đăng ký user mới**
2. **Login với username/email**
3. **Email verification flow**
4. **Password reset flow**
5. **OAuth Google login**
6. **Check email/username availability**

## 📖 Swagger UI

Access full API documentation at:
```
http://localhost:8080/api/v1/swagger-ui/index.html
```

### 📂 **API Categories trong Swagger:**
- 🔐 **Authentication** - Comprehensive authentication system
- 🔗 **OAuth** - OAuth2 integration with Google
- 🏘️ **Properties** - Property CRUD operations
- 💳 **Payments** - Multi-gateway payment processing
- 🔔 **Notifications** - Real-time notification system
- 👨‍💼 **Admin** - Administrative operations

## 🚀 Next Steps

1. **Config real SMTP** cho production
2. **Setup Google OAuth** với real client credentials  
3. **Custom email templates** theo brand identity
4. **Add social logins** (Facebook, GitHub)
5. **Implement 2FA** cho admin accounts
6. **Add password strength** requirements
7. **Rate limiting** for auth endpoints

---
*📧 Liên hệ hỗ trợ: <EMAIL>* 