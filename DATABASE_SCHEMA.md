# Database Schema Documentation

## Overview

This document describes the database schema for the Real Estate Membership Platform. The system uses PostgreSQL as the primary database.

## Core Tables

### 1. users
User accounts and authentication information.

```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HAR(50) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    phone_number VARCHAR(20),
    role VARCHAR(20) DEFAULT 'USER' CHECK (role IN ('USER', 'ADMIN')),
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE', 'BANNED')),
    email_verified BOOLEAN DEFAULT FALSE,
    oauth_provider VARCHAR(20) DEFAULT 'LOCAL',
    oauth_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2. memberships
Available membership plans.

```sql
CREATE TABLE memberships (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    duration_months INTEGER NOT NULL,
    max_properties INTEGER NOT NULL,
    featured_properties INTEGER DEFAULT 0,
    priority_support BOOLEAN DEFAULT FALSE,
    analytics_access BOOLEAN DEFAULT FALSE,
    multiple_images BOOLEAN DEFAULT FALSE,
    contact_info_display BOOLEAN DEFAULT TRUE,
    ai_content_generation BOOLEAN DEFAULT FALSE,
    push_top_limit INTEGER DEFAULT 0,
    type VARCHAR(20) DEFAULT 'BASIC' CHECK (type IN ('BASIC', 'PREMIUM')),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. user_memberships
User subscription records.

```sql
CREATE TABLE user_memberships (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    membership_id BIGINT NOT NULL REFERENCES memberships(id),
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP NOT NULL,
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'EXPIRED', 'CANCELLED', 'SUSPENDED')),
    auto_renewal BOOLEAN DEFAULT FALSE,
    properties_used INTEGER DEFAULT 0,
    push_top_used INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 4. categories
Property categories.

```sql
CREATE TABLE categories (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 5. properties
Real estate listings.

```sql
CREATE TABLE properties (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    category_id BIGINT NOT NULL REFERENCES categories(id),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    price DECIMAL(15,2) NOT NULL,
    address VARCHAR(255) NOT NULL,
    city VARCHAR(100) NOT NULL,
    district VARCHAR(100),
    ward VARCHAR(100),
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    property_area DECIMAL(10,2),
    land_area DECIMAL(10,2),
    bedrooms INTEGER,
    bathrooms INTEGER,
    floors INTEGER,
    property_type VARCHAR(20) CHECK (property_type IN ('APARTMENT', 'HOUSE', 'VILLA', 'LAND', 'OFFICE', 'SHOP')),
    listing_type VARCHAR(20) CHECK (listing_type IN ('SALE', 'RENT')),
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'APPROVED', 'REJECTED', 'SOLD', 'RENTED', 'EXPIRED')),
    is_featured BOOLEAN DEFAULT FALSE,
    view_count INTEGER DEFAULT 0,
    contact_count INTEGER DEFAULT 0,
    published_at TIMESTAMP,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 6. property_images
Property photos and media.

```sql
CREATE TABLE property_images (
    id BIGSERIAL PRIMARY KEY,
    property_id BIGINT NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
    image_url VARCHAR(500) NOT NULL,
    image_key VARCHAR(255),
    is_primary BOOLEAN DEFAULT FALSE,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 7. payments
Payment transaction records.

```sql
CREATE TABLE payments (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id),
    membership_id BIGINT NOT NULL REFERENCES memberships(id),
    stripe_payment_intent_id VARCHAR(255),
    stripe_session_id VARCHAR(255),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'COMPLETED', 'FAILED', 'CANCELLED', 'REFUNDED')),
    payment_method VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 8. property_boosts
Push-to-top feature records.

```sql
CREATE TABLE property_boosts (
    id BIGSERIAL PRIMARY KEY,
    property_id BIGINT NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
    user_id BIGINT NOT NULL REFERENCES users(id),
    boost_type VARCHAR(20) DEFAULT 'PUSH_TOP' CHECK (boost_type IN ('PUSH_TOP', 'FEATURED')),
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Additional Tables

### 9. admin_actions
Admin activity logging.

```sql
CREATE TABLE admin_actions (
    id BIGSERIAL PRIMARY KEY,
    admin_id BIGINT NOT NULL REFERENCES users(id),
    action_type VARCHAR(50) NOT NULL,
    target_type VARCHAR(50),
    target_id BIGINT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 10. ai_agents
AI chatbot configuration.

```sql
CREATE TABLE ai_agents (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    system_prompt TEXT NOT NULL,
    model VARCHAR(50) DEFAULT 'gpt-3.5-turbo',
    temperature DECIMAL(3,2) DEFAULT 0.7,
    max_tokens INTEGER DEFAULT 1000,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Indexes

```sql
-- Performance indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_properties_user_id ON properties(user_id);
CREATE INDEX idx_properties_status ON properties(status);
CREATE INDEX idx_properties_city_district ON properties(city, district);
CREATE INDEX idx_properties_price ON properties(price);
CREATE INDEX idx_user_memberships_user_id ON user_memberships(user_id);
CREATE INDEX idx_user_memberships_status ON user_memberships(status);
CREATE INDEX idx_payments_user_id ON payments(user_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_property_boosts_property_id ON property_boosts(property_id);
CREATE INDEX idx_property_boosts_active ON property_boosts(is_active, start_time, end_time);
```

## Sample Data

### Default Memberships
```sql
INSERT INTO memberships (name, description, price, duration_months, max_properties, ai_content_generation, push_top_limit, type) VALUES
('FREE', 'Free membership for new users', 0.00, 1, 10, FALSE, 0, 'BASIC'),
('BASIC', 'Basic membership with standard features', 99.00, 1, 10, FALSE, 0, 'BASIC'),
('PREMIUM', 'Premium membership with AI and advanced features', 299.00, 1, 50, TRUE, 10, 'PREMIUM');
```

### Default Categories
```sql
INSERT INTO categories (name, description, icon) VALUES
('Apartment', 'Apartment and condominium listings', 'apartment'),
('House', 'Single-family houses and townhouses', 'house'),
('Villa', 'Luxury villas and mansions', 'villa'),
('Land', 'Land plots and development sites', 'land'),
('Office', 'Commercial office spaces', 'office'),
('Shop', 'Retail and commercial shops', 'shop');
```

### Default Admin User
```sql
INSERT INTO users (username, email, password, first_name, last_name, role, status, email_verified) VALUES
('admin', '<EMAIL>', '$2a$10$encrypted_password_hash', 'System', 'Administrator', 'ADMIN', 'ACTIVE', TRUE);
```

## Business Rules

1. **User Registration**: New users automatically get FREE membership for 30 days
2. **Property Status**: All properties start as PENDING and require admin approval
3. **Membership Limits**: Users cannot exceed their membership's max_properties limit
4. **Push Top Feature**: Only available for PREMIUM users, limited to 10 uses per month
5. **AI Content**: Only available for PREMIUM membership users
6. **Payment Processing**: Stripe handles all payment processing with webhook integration

## Migration Notes for Go

When migrating to Go, consider:
- Use GORM or similar ORM for database operations
- Implement proper connection pooling
- Add database migrations system
- Consider using UUID for primary keys instead of BIGSERIAL
- Implement proper foreign key constraints
- Add database health checks
- Consider read replicas for better performance
