# 📚 Swagger UI Guide - Real Estate Membership API

## 🚀 Access Swagger UI

**Development Server:**
```
http://localhost:8080/api/v1/swagger-ui/index.html
```

**API Documentation (JSON):**
```
http://localhost:8080/api/v1/api-docs
```

## 🌟 Updated Features in Swagger UI

### ✨ What's New:
- **🎨 Enhanced UI** with emoji-based category organization
- **💳 Payment Gateway Documentation** for VNPay, MoMo, and Stripe
- **🔔 Notification System APIs** with multi-channel delivery
- **👨‍💼 Admin Management** endpoints for user and content management
- **🤖 AI Chatbot** integration documentation
- **🔐 Comprehensive Security** documentation

### 📂 API Categories:

#### 🔐 Authentication
- User registration and login
- JWT token management
- OAuth2 Google integration

#### 🏘️ Properties
- Property CRUD operations
- Advanced search and filtering
- Image upload and management
- SEO-friendly slug generation

#### 👤 Memberships
- Subscription plan management
- User membership tracking
- Membership history and renewal

#### 💳 Payments
- **VNPay Integration** (Vietnam's leading payment gateway)
- **MoMo Integration** (Popular e-wallet in Vietnam)
- **Stripe Integration** (International credit card processing)
- Automatic membership activation
- Webhook security with signature verification
- Multi-currency support (VND, USD)

#### 🔔 Notifications
- **20+ Notification Types**: Payment success, property approval, membership expiry
- **Multi-Channel Delivery**: Email, Push notifications, SMS
- **Scheduled Notifications**: Membership expiry warnings (7/3/1 days)
- **Real-time In-App**: Action buttons, read/unread status
- **Automatic Retry**: Failed notifications retry with exponential backoff

#### 👨‍💼 Admin
- User management and analytics
- Property approval/rejection workflow
- Payment and transaction management
- System health monitoring
- Content moderation tools

#### 🤖 AI Chatbot
- AI-powered property search assistance
- Conversation history management
- Enhanced property recommendations
- OpenAI GPT-4 integration

#### 📂 Categories
- Property category management
- Category-based property filtering

#### 🔗 OAuth
- Google OAuth2 integration
- Account linking functionality
- OAuth user management

## 🔑 Authentication Guide

### 1. Get JWT Token
```bash
POST /api/v1/auth/login
{
  "username": "admin",
  "password": "admin123"
}
```

### 2. Use Token in Swagger UI
1. Click the **🔒 Authorize** button in Swagger UI
2. Enter: `Bearer YOUR_JWT_TOKEN`
3. Click **Authorize**

### 3. Test Endpoints
All secured endpoints will now work with your token!

## 🧪 Test Data

### Default Admin User:
- **Username:** `admin`
- **Password:** `admin123`
- **Email:** `<EMAIL>`

### Payment Testing:
- **VNPay:** Use sandbox environment test cards
- **MoMo:** Use test API credentials (already configured)
- **Stripe:** Use test cards from Stripe documentation

### Webhooks:
All payment webhooks are configured for `localhost:8080`

## 📋 Quick Start Examples

### 1. Create Property
```bash
POST /api/v1/properties
Authorization: Bearer YOUR_TOKEN
{
  "title": "Beautiful House",
  "description": "A wonderful property...",
  "price": 1000000,
  "categoryId": 1,
  "address": "123 Main St",
  "city": "Ho Chi Minh City"
}
```

### 2. Process Payment
```bash
POST /api/v1/payments/vnpay/create
Authorization: Bearer YOUR_TOKEN
{
  "membershipId": 1,
  "paymentMethod": "VNPAY",
  "amount": 99000
}
```

### 3. Send Notification
```bash
POST /api/v1/notifications (Admin endpoint)
Authorization: Bearer YOUR_TOKEN
{
  "userId": 1,
  "type": "MEMBERSHIP_ACTIVATED",
  "title": "Membership Activated!",
  "message": "Your premium membership is now active."
}
```

### 4. Chat with AI
```bash
POST /api/v1/chatbot/chat
Authorization: Bearer YOUR_TOKEN
{
  "message": "Find me a house in District 1 with 3 bedrooms under 2 million"
}
```

## 🛡️ Security Features

- **JWT Authentication** with refresh tokens
- **Role-based Access Control** (USER/ADMIN)
- **Payment Webhook Signature Verification**
- **CORS Protection** with configurable origins
- **Input Validation** and sanitization
- **BCrypt Password Hashing**

## 🔧 API Configuration

### Development Environment:
- **Server:** `http://localhost:8080/api/v1`
- **Database:** H2 in-memory (for development)
- **Payment Gateways:** Test/Sandbox mode
- **Email:** Mock SMTP settings

### Production Ready:
- PostgreSQL database support
- Real payment gateway credentials
- Production SMTP configuration
- AWS S3 integration for file uploads

## 📈 Version Information

- **API Version:** v2.0.0
- **Documentation:** Auto-generated with comprehensive examples
- **Last Updated:** Includes payment gateways and notification system
- **License:** MIT License

## 🔗 Additional Resources

- **GitHub Repository:** https://github.com/realestate/membership
- **Contact:** <EMAIL>
- **API Specification:** OpenAPI 3.0.1 compliant

---

💡 **Pro Tip:** Use the "Try it out" feature in Swagger UI to test endpoints directly from the browser! 