# ===============================================
# REAL ESTATE MEMBERSHIP PLATFORM
# Application Configuration Template
# ===============================================

# ===============================================
# SERVER CONFIGURATION
# ===============================================
server.port=8080
server.servlet.context-path=/api/v1

# ===============================================
# DATABASE CONFIGURATION
# ===============================================
# PostgreSQL Database (AWS RDS or Local)
spring.datasource.url=*************************************************************************************************
spring.datasource.username=realestate_user
spring.datasource.password=your_secure_password_here
spring.datasource.driver-class-name=org.postgresql.Driver

# Connection Pool Settings
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# ===============================================
# JPA/HIBERNATE CONFIGURATION
# ===============================================
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true

# ===============================================
# JWT CONFIGURATION
# ===============================================
app.jwt.secret=mySecretKey123456789012345678901234567890
app.jwt.expiration=86400000

# ===============================================
# ADMIN CONFIGURATION
# ===============================================
app.admin.username=admin
app.admin.email=<EMAIL>
app.admin.password=admin123

# ===============================================
# AWS S3 CONFIGURATION
# ===============================================
aws.s3.bucket-name=realestate-images-bucket
aws.s3.region=us-east-1
aws.access-key-id=YOUR_AWS_ACCESS_KEY
aws.secret-access-key=YOUR_AWS_SECRET_KEY

# ===============================================
# STRIPE PAYMENT CONFIGURATION
# ===============================================
stripe.api.key=sk_test_your_stripe_secret_key_here
stripe.webhook.secret=whsec_your_webhook_secret_here
stripe.currency=usd

# ===============================================
# OPENAI CONFIGURATION
# ===============================================
openai.api.key=sk-your_openai_api_key_here
openai.model=gpt-3.5-turbo
openai.max-tokens=1000
openai.temperature=0.7

# ===============================================
# FILE UPLOAD CONFIGURATION
# ===============================================
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
spring.servlet.multipart.enabled=true

# ===============================================
# LOGGING CONFIGURATION
# ===============================================
logging.level.com.realestate.membership=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.web=INFO
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# ===============================================
# ACTUATOR CONFIGURATION
# ===============================================
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized

# ===============================================
# CORS CONFIGURATION
# ===============================================
app.cors.allowed-origins=http://localhost:3000,http://localhost:8080
app.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
app.cors.allowed-headers=*
app.cors.allow-credentials=true

# ===============================================
# NOTIFICATION CONFIGURATION
# ===============================================
app.notification.email.enabled=true
app.notification.sms.enabled=false

# ===============================================
# CACHE CONFIGURATION
# ===============================================
spring.cache.type=simple
spring.cache.cache-names=properties,users,memberships

# ===============================================
# SECURITY CONFIGURATION
# ===============================================
app.security.password.min-length=8
app.security.jwt.refresh-token-expiration=604800000
app.security.rate-limit.enabled=true

# ===============================================
# BUSINESS LOGIC CONFIGURATION
# ===============================================
app.membership.free.duration-months=1
app.membership.free.max-properties=10
app.membership.basic.max-properties=10
app.membership.premium.max-properties=50
app.membership.premium.push-top-limit=10

# ===============================================
# EMAIL CONFIGURATION (Optional)
# ===============================================
# spring.mail.host=smtp.gmail.com
# spring.mail.port=587
# spring.mail.username=<EMAIL>
# spring.mail.password=your-app-password
# spring.mail.properties.mail.smtp.auth=true
# spring.mail.properties.mail.smtp.starttls.enable=true

# ===============================================
# DEVELOPMENT/PRODUCTION PROFILES
# ===============================================
# Uncomment for production
# spring.profiles.active=prod
# logging.level.com.realestate.membership=INFO
# spring.jpa.show-sql=false

# ===============================================
# MONITORING & METRICS
# ===============================================
# management.metrics.export.prometheus.enabled=true
# management.endpoint.prometheus.enabled=true
