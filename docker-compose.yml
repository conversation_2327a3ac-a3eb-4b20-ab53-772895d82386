# =====================================================
# DOCKER COMPOSE FOR REAL ESTATE MEMBERSHIP SYSTEM
# Production-ready setup for EC2
# =====================================================

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: realestate-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: realestate_membership
      POSTGRES_USER: realestate_user
      POSTGRES_PASSWORD: ${DB_PASSWORD:-realestate_password_2024}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - realestate-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U realestate_user -d realestate_membership"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache (Optional)
  redis:
    image: redis:7-alpine
    container_name: realestate-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - realestate-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Spring Boot Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: realestate-app
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      # Database Configuration
      SPRING_DATASOURCE_URL: *****************************************************
      SPRING_DATASOURCE_USERNAME: realestate_user
      SPRING_DATASOURCE_PASSWORD: ${DB_PASSWORD:-realestate_password_2024}
      
      # Redis Configuration
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
      
      # Application Configuration
      SPRING_PROFILES_ACTIVE: docker
      SERVER_PORT: 8080
      
      # JWT Configuration
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-this-in-production}
      JWT_EXPIRATION: 86400000
      APP_JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-this-in-production}
      
      # OpenAI Configuration
      OPENAI_API_KEY: ${OPENAI_API_KEY:-}
      OPENAI_API_MODEL: gpt-3.5-turbo
      
      # AWS S3 Configuration
      AWS_S3_REGION: ${AWS_S3_REGION:-ap-southeast-2}
      AWS_S3_BUCKET_NAME: ${AWS_S3_BUCKET_NAME:-}
      AWS_S3_ACCESS_KEY: ${AWS_S3_ACCESS_KEY:-}
      AWS_S3_SECRET_KEY: ${AWS_S3_SECRET_KEY:-}
      AWS_S3_ENDPOINT: ${AWS_S3_ENDPOINT:-https://s3.ap-southeast-2.amazonaws.com}
      AWS_S3_CDN_DOMAIN: ${AWS_S3_CDN_DOMAIN:-}
      
      # Payment Gateway Configuration
      VNPAY_TMN_CODE: ${VNPAY_TMN_CODE:-}
      VNPAY_SECRET_KEY: ${VNPAY_SECRET_KEY:-}
      MOMO_PARTNER_CODE: ${MOMO_PARTNER_CODE:-}
      MOMO_ACCESS_KEY: ${MOMO_ACCESS_KEY:-}
      MOMO_SECRET_KEY: ${MOMO_SECRET_KEY:-}
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY:-}
      
      # Email Configuration
      SPRING_MAIL_HOST: ${MAIL_HOST:-smtp.gmail.com}
      SPRING_MAIL_PORT: ${MAIL_PORT:-587}
      SPRING_MAIL_USERNAME: ${MAIL_USERNAME:-}
      SPRING_MAIL_PASSWORD: ${MAIL_PASSWORD:-}
      
      # Frontend URL
      APP_FRONTEND_URL: ${FRONTEND_URL:-http://localhost:3000}

      # Google OAuth Configuration
      SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID:-mock-client-id}
      SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GOOGLE_CLIENT_SECRET: ${GOOGLE_CLIENT_SECRET:-mock-client-secret}
      
      # Java Options (optimized for Java 24)
      JAVA_OPTS: "-Xmx1g -Xms512m -XX:+UseG1GC -XX:+UseStringDeduplication --enable-preview"
      
    ports:
      - "8080:8080"
    volumes:
      - app_uploads:/app/uploads
      - app_logs:/app/logs
    networks:
      - realestate-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/v1/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: realestate-nginx
    restart: unless-stopped
    depends_on:
      - app
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - app_uploads:/var/www/uploads:ro
    networks:
      - realestate-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

# Networks
networks:
  realestate-network:
    driver: bridge
    name: realestate-network

# Volumes
volumes:
  postgres_data:
    driver: local
    name: realestate-postgres-data
  redis_data:
    driver: local
    name: realestate-redis-data
  app_uploads:
    driver: local
    name: realestate-app-uploads
  app_logs:
    driver: local
    name: realestate-app-logs
