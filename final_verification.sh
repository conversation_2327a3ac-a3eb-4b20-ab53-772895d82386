#!/bin/bash

echo "🔍 FINAL API VERIFICATION"
echo "========================"

BASE_URL="http://localhost:8080/api/v1"

# Test 1: Admin Login
echo "1. Testing Admin Login..."
ADMIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}')

if echo "$ADMIN_RESPONSE" | grep -q "token"; then
    echo "✅ Admin Login: SUCCESS"
    ADMIN_TOKEN=$(echo "$ADMIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
else
    echo "❌ Admin Login: FAILED"
    echo "Response: $ADMIN_RESPONSE"
fi

# Test 2: Public Endpoints
echo -e "\n2. Testing Public Endpoints..."

# Memberships
MEMBERSHIPS_RESPONSE=$(curl -s "$BASE_URL/memberships")
if echo "$MEMBERSHIPS_RESPONSE" | grep -q "\["; then
    echo "✅ Memberships API: SUCCESS"
else
    echo "❌ Memberships API: FAILED"
fi

# Categories  
CATEGORIES_RESPONSE=$(curl -s "$BASE_URL/categories")
if echo "$CATEGORIES_RESPONSE" | grep -q "\["; then
    echo "✅ Categories API: SUCCESS"
else
    echo "❌ Categories API: FAILED"
fi

# Properties
PROPERTIES_RESPONSE=$(curl -s "$BASE_URL/properties")
if echo "$PROPERTIES_RESPONSE" | grep -q "\["; then
    echo "✅ Properties API: SUCCESS"
else
    echo "❌ Properties API: FAILED"
fi

# Payment Methods
PAYMENT_METHODS_RESPONSE=$(curl -s "$BASE_URL/payments/methods")
if echo "$PAYMENT_METHODS_RESPONSE" | grep -q "\["; then
    echo "✅ Payment Methods API: SUCCESS"
else
    echo "❌ Payment Methods API: FAILED"
fi

# Exchange Rate
EXCHANGE_RATE_RESPONSE=$(curl -s "$BASE_URL/payments/exchange-rate")
if echo "$EXCHANGE_RATE_RESPONSE" | grep -q "rate\|usd\|vnd"; then
    echo "✅ Exchange Rate API: SUCCESS"
else
    echo "❌ Exchange Rate API: FAILED"
fi

# Test 3: User Registration
echo -e "\n3. Testing User Registration..."
USER_REG_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/register" \
  -H "Content-Type: application/json" \
  -d '{"username":"finaltest","email":"<EMAIL>","password":"password123","firstName":"Final","lastName":"Test"}')

if echo "$USER_REG_RESPONSE" | grep -q "success\|created\|registered\|token"; then
    echo "✅ User Registration: SUCCESS"
else
    echo "❌ User Registration: FAILED"
    echo "Response: $USER_REG_RESPONSE"
fi

# Test 4: User Login
echo -e "\n4. Testing User Login..."
USER_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"finaltest","password":"password123"}')

if echo "$USER_RESPONSE" | grep -q "token"; then
    echo "✅ User Login: SUCCESS"
    USER_TOKEN=$(echo "$USER_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
else
    echo "❌ User Login: FAILED"
    echo "Response: $USER_RESPONSE"
fi

# Test 5: Admin Endpoints
if [ -n "$ADMIN_TOKEN" ]; then
    echo -e "\n5. Testing Admin Endpoints..."
    
    ADMIN_USERS_RESPONSE=$(curl -s "$BASE_URL/admin/users" \
      -H "Authorization: Bearer $ADMIN_TOKEN")
    
    if echo "$ADMIN_USERS_RESPONSE" | grep -q "\["; then
        echo "✅ Admin Users API: SUCCESS"
    else
        echo "❌ Admin Users API: FAILED"
    fi
fi

# Test 6: User Authenticated Endpoints
if [ -n "$USER_TOKEN" ]; then
    echo -e "\n6. Testing User Authenticated Endpoints..."
    
    NOTIFICATIONS_RESPONSE=$(curl -s "$BASE_URL/notifications" \
      -H "Authorization: Bearer $USER_TOKEN")
    
    if echo "$NOTIFICATIONS_RESPONSE" | grep -q "\["; then
        echo "✅ User Notifications API: SUCCESS"
    else
        echo "❌ User Notifications API: FAILED"
    fi
    
    UNREAD_COUNT_RESPONSE=$(curl -s "$BASE_URL/notifications/unread/count" \
      -H "Authorization: Bearer $USER_TOKEN")
    
    if echo "$UNREAD_COUNT_RESPONSE" | grep -q "[0-9]"; then
        echo "✅ Unread Count API: SUCCESS"
    else
        echo "❌ Unread Count API: FAILED"
    fi
fi

echo -e "\n🎉 FINAL VERIFICATION COMPLETE!"
echo "================================"
