#!/bin/bash

# =====================================================
# INSTALL JAVA 21 LTS SCRIPT
# For macOS and Linux
# =====================================================

echo "🚀 Installing Java 21 LTS..."

# Detect OS
OS="$(uname -s)"
case "${OS}" in
    Linux*)     MACHINE=Linux;;
    Darwin*)    MACHINE=Mac;;
    *)          MACHINE="UNKNOWN:${OS}"
esac

echo "🖥️  Detected OS: $MACHINE"

if [ "$MACHINE" = "Mac" ]; then
    echo "📦 Installing Java 21 LTS on macOS..."

    # Check if Homebrew is installed
    if ! command -v brew &> /dev/null; then
        echo "❌ Homebrew not found. Please install Homebrew first:"
        echo "   /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        exit 1
    fi

    # Install Java 21 using Homebrew
    echo "🔧 Installing OpenJDK 21..."
    brew install openjdk@21

    # Create symlink
    echo "🔗 Creating symlink..."
    sudo ln -sfn /opt/homebrew/opt/openjdk@21/libexec/openjdk.jdk /Library/Java/JavaVirtualMachines/openjdk-21.jdk

    # Set JAVA_HOME
    echo "🌍 Setting JAVA_HOME..."
    export JAVA_HOME=/opt/homebrew/opt/openjdk@21/libexec/openjdk.jdk/Contents/Home
    
    # Add to shell profile
    SHELL_PROFILE=""
    if [ -f ~/.zshrc ]; then
        SHELL_PROFILE=~/.zshrc
    elif [ -f ~/.bash_profile ]; then
        SHELL_PROFILE=~/.bash_profile
    elif [ -f ~/.bashrc ]; then
        SHELL_PROFILE=~/.bashrc
    fi
    
    if [ -n "$SHELL_PROFILE" ]; then
        echo "📝 Adding JAVA_HOME to $SHELL_PROFILE..."
        echo "" >> $SHELL_PROFILE
        echo "# Java 21 LTS" >> $SHELL_PROFILE
        echo "export JAVA_HOME=/opt/homebrew/opt/openjdk@21/libexec/openjdk.jdk/Contents/Home" >> $SHELL_PROFILE
        echo "export PATH=\$JAVA_HOME/bin:\$PATH" >> $SHELL_PROFILE
    fi
    
elif [ "$MACHINE" = "Linux" ]; then
    echo "📦 Installing Java 21 LTS on Linux..."
    
    # Check if running on Amazon Linux/CentOS/RHEL
    if [ -f /etc/redhat-release ]; then
        echo "🔧 Installing on RedHat-based system..."
        
        # Install OpenJDK 21 LTS
        sudo yum install -y java-21-openjdk java-21-openjdk-devel

        # Set alternatives
        sudo alternatives --install /usr/bin/java java /usr/lib/jvm/java-21-openjdk/bin/java 1
        sudo alternatives --install /usr/bin/javac javac /usr/lib/jvm/java-21-openjdk/bin/javac 1

        # Set JAVA_HOME
        echo "🌍 Setting JAVA_HOME..."
        echo "export JAVA_HOME=/usr/lib/jvm/java-21-openjdk" | sudo tee -a /etc/environment
        echo "export PATH=\$JAVA_HOME/bin:\$PATH" | sudo tee -a /etc/environment
        
    # Check if running on Ubuntu/Debian
    elif [ -f /etc/debian_version ]; then
        echo "🔧 Installing on Debian-based system..."
        
        # Update package list
        sudo apt update
        
        # Install Java 21 LTS
        sudo apt update
        sudo apt install -y openjdk-21-jdk

        # Set alternatives
        sudo update-alternatives --install /usr/bin/java java /usr/lib/jvm/java-21-openjdk-amd64/bin/java 1
        sudo update-alternatives --install /usr/bin/javac javac /usr/lib/jvm/java-21-openjdk-amd64/bin/javac 1

        # Set JAVA_HOME
        echo "🌍 Setting JAVA_HOME..."
        echo "export JAVA_HOME=/usr/lib/jvm/java-21-openjdk-amd64" | sudo tee -a /etc/environment
        echo "export PATH=\$JAVA_HOME/bin:\$PATH" | sudo tee -a /etc/environment
    fi
    
    # Source environment
    source /etc/environment
fi

echo ""
echo "✅ Java 21 LTS installation completed!"
echo ""
echo "🔍 Verifying installation..."
java -version
javac -version

echo ""
echo "🌍 JAVA_HOME: $JAVA_HOME"
echo ""
echo "📝 Please restart your terminal or run:"
echo "   source ~/.zshrc    # for zsh"
echo "   source ~/.bashrc   # for bash"
echo ""
echo "🚀 Ready to build with Java 21 LTS!"
