#!/bin/bash

# =====================================================
# INSTALL JAVA 24 SCRIPT
# For macOS and Linux
# =====================================================

echo "🚀 Installing Java 24..."

# Detect OS
OS="$(uname -s)"
case "${OS}" in
    Linux*)     MACHINE=Linux;;
    Darwin*)    MACHINE=Mac;;
    *)          MACHINE="UNKNOWN:${OS}"
esac

echo "🖥️  Detected OS: $MACHINE"

if [ "$MACHINE" = "Mac" ]; then
    echo "📦 Installing Java 24 on macOS..."
    
    # Check if Homebrew is installed
    if ! command -v brew &> /dev/null; then
        echo "❌ Homebrew not found. Please install Homebrew first:"
        echo "   /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        exit 1
    fi
    
    # Install Java 24 using Homebrew
    echo "🔧 Installing OpenJDK 24..."
    brew install openjdk@24
    
    # Create symlink
    echo "🔗 Creating symlink..."
    sudo ln -sfn /opt/homebrew/opt/openjdk@24/libexec/openjdk.jdk /Library/Java/JavaVirtualMachines/openjdk-24.jdk
    
    # Set JAVA_HOME
    echo "🌍 Setting JAVA_HOME..."
    export JAVA_HOME=/opt/homebrew/opt/openjdk@24/libexec/openjdk.jdk/Contents/Home
    
    # Add to shell profile
    SHELL_PROFILE=""
    if [ -f ~/.zshrc ]; then
        SHELL_PROFILE=~/.zshrc
    elif [ -f ~/.bash_profile ]; then
        SHELL_PROFILE=~/.bash_profile
    elif [ -f ~/.bashrc ]; then
        SHELL_PROFILE=~/.bashrc
    fi
    
    if [ -n "$SHELL_PROFILE" ]; then
        echo "📝 Adding JAVA_HOME to $SHELL_PROFILE..."
        echo "" >> $SHELL_PROFILE
        echo "# Java 24" >> $SHELL_PROFILE
        echo "export JAVA_HOME=/opt/homebrew/opt/openjdk@24/libexec/openjdk.jdk/Contents/Home" >> $SHELL_PROFILE
        echo "export PATH=\$JAVA_HOME/bin:\$PATH" >> $SHELL_PROFILE
    fi
    
elif [ "$MACHINE" = "Linux" ]; then
    echo "📦 Installing Java 24 on Linux..."
    
    # Check if running on Amazon Linux/CentOS/RHEL
    if [ -f /etc/redhat-release ]; then
        echo "🔧 Installing on RedHat-based system..."
        
        # Download and install OpenJDK 24
        cd /tmp
        wget https://download.java.net/java/GA/jdk24/2d4d2e8e8b8b4b8b8b8b8b8b8b8b8b8b/36/GPL/openjdk-24_linux-x64_bin.tar.gz
        
        sudo mkdir -p /opt/java
        sudo tar -xzf openjdk-24_linux-x64_bin.tar.gz -C /opt/java
        sudo mv /opt/java/jdk-24* /opt/java/jdk-24
        
        # Set alternatives
        sudo alternatives --install /usr/bin/java java /opt/java/jdk-24/bin/java 1
        sudo alternatives --install /usr/bin/javac javac /opt/java/jdk-24/bin/javac 1
        
        # Set JAVA_HOME
        echo "🌍 Setting JAVA_HOME..."
        echo "export JAVA_HOME=/opt/java/jdk-24" | sudo tee -a /etc/environment
        echo "export PATH=\$JAVA_HOME/bin:\$PATH" | sudo tee -a /etc/environment
        
    # Check if running on Ubuntu/Debian
    elif [ -f /etc/debian_version ]; then
        echo "🔧 Installing on Debian-based system..."
        
        # Update package list
        sudo apt update
        
        # Install Java 24 (if available in repos, otherwise download manually)
        if apt-cache search openjdk-24 | grep -q openjdk-24; then
            sudo apt install -y openjdk-24-jdk
        else
            # Download and install manually
            cd /tmp
            wget https://download.java.net/java/GA/jdk24/2d4d2e8e8b8b4b8b8b8b8b8b8b8b8b8b/36/GPL/openjdk-24_linux-x64_bin.tar.gz
            
            sudo mkdir -p /opt/java
            sudo tar -xzf openjdk-24_linux-x64_bin.tar.gz -C /opt/java
            sudo mv /opt/java/jdk-24* /opt/java/jdk-24
            
            # Set alternatives
            sudo update-alternatives --install /usr/bin/java java /opt/java/jdk-24/bin/java 1
            sudo update-alternatives --install /usr/bin/javac javac /opt/java/jdk-24/bin/javac 1
        fi
        
        # Set JAVA_HOME
        echo "🌍 Setting JAVA_HOME..."
        echo "export JAVA_HOME=/opt/java/jdk-24" | sudo tee -a /etc/environment
        echo "export PATH=\$JAVA_HOME/bin:\$PATH" | sudo tee -a /etc/environment
    fi
    
    # Source environment
    source /etc/environment
fi

echo ""
echo "✅ Java 24 installation completed!"
echo ""
echo "🔍 Verifying installation..."
java -version
javac -version

echo ""
echo "🌍 JAVA_HOME: $JAVA_HOME"
echo ""
echo "📝 Please restart your terminal or run:"
echo "   source ~/.zshrc    # for zsh"
echo "   source ~/.bashrc   # for bash"
echo ""
echo "🚀 Ready to build with Java 24!"
