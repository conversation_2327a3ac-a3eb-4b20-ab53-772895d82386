#!/bin/bash
# =====================================================
# SSL SETUP SCRIPT FOR DOMAIN
# Automatic Let's Encrypt SSL certificate setup
# =====================================================

set -e

DOMAIN="api.thehomefinder.xyz"
EMAIL="<EMAIL>"  # Change this to your email

echo "🔧 Setting up SSL for $DOMAIN..."

# Install certbot
sudo yum update -y
sudo yum install -y certbot python3-certbot-nginx

# Create certbot directory
sudo mkdir -p /var/www/certbot

# Stop nginx temporarily
docker stop realestate-nginx || true

# Generate SSL certificate
sudo certbot certonly \
    --standalone \
    --email $EMAIL \
    --agree-tos \
    --no-eff-email \
    --domain $DOMAIN

# Update docker-compose to include SSL volumes
echo "🔧 Updating docker-compose with SSL volumes..."

# Restart with SSL support
docker-compose -f docker-compose-rds.yml down
docker-compose -f docker-compose-rds.yml up -d

echo "✅ SSL setup completed!"
echo "🌐 Your API is now available at: https://$DOMAIN/api/v1/swagger-ui/index.html"
