package com.realestate.membership.controller;

import com.realestate.membership.entity.User;
import com.realestate.membership.service.AIContentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/ai-content")
@RequiredArgsConstructor
@Tag(name = "🤖 AI Content", description = "AI-powered content generation for premium users")
public class AIContentController {
    
    private final AIContentService aiContentService;
    
    @PostMapping("/generate")
    @Operation(summary = "Generate SEO-optimized property content", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Map<String, Object>> generatePropertyContent(
            @Valid @RequestBody AIContentRequest request,
            @AuthenticationPrincipal User currentUser) {
        
        Map<String, Object> result = aiContentService.generatePropertyContent(
                currentUser.getId(), 
                request.getDescription(), 
                request.getImageDescription()
        );
        return ResponseEntity.ok(result);
    }
    
    // DTO for request
    public static class AIContentRequest {
        private String description;
        private String imageDescription;
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public String getImageDescription() { return imageDescription; }
        public void setImageDescription(String imageDescription) { this.imageDescription = imageDescription; }
    }
}
