package com.realestate.membership.controller;

import com.realestate.membership.entity.User;
import com.realestate.membership.service.AIContentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/ai-content")
@RequiredArgsConstructor
@Tag(name = "🤖 AI Content", description = "AI-powered content generation for premium users")
public class AIContentController {
    
    private final AIContentService aiContentService;
    
    @PostMapping("/generate")
    @Operation(summary = "Generate SEO-optimized property content", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Map<String, Object>> generatePropertyContent(
            @Valid @RequestBody AIContentRequest request,
            @AuthenticationPrincipal User currentUser) {
        
        Map<String, Object> result = aiContentService.generatePropertyContent(
                currentUser.getId(), 
                request.getDescription(), 
                request.getImageDescription()
        );
        return ResponseEntity.ok(result);
    }
    
    // DTO for request
    @Schema(description = "Request for AI content generation")
    public static class AIContentRequest {

        @NotBlank(message = "Description is required")
        @Size(min = 10, max = 1000, message = "Description must be between 10 and 1000 characters")
        @Schema(description = "Property description to enhance with AI",
                example = "Căn hộ 2 phòng ngủ tại quận 1, gần chợ Bến Thành")
        private String description;

        @Size(max = 500, message = "Image description must not exceed 500 characters")
        @Schema(description = "Optional description of property images",
                example = "Phòng khách rộng rãi, view thành phố")
        private String imageDescription;

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public String getImageDescription() { return imageDescription; }
        public void setImageDescription(String imageDescription) { this.imageDescription = imageDescription; }
    }
}
