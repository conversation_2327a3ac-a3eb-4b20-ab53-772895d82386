package com.realestate.membership.controller;

import com.realestate.membership.dto.PropertyResponse;
import com.realestate.membership.dto.AdminStatsResponse;
import com.realestate.membership.dto.UserManagementResponse;
import com.realestate.membership.entity.Property;
import com.realestate.membership.entity.User;
import com.realestate.membership.service.AdminService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

@RestController
@RequestMapping("/admin")
@RequiredArgsConstructor
@PreAuthorize("hasRole('ADMIN')")
@Tag(name = "👨‍💼 Admin", description = "Administrative operations for user and content management")
public class AdminController {

    private final AdminService adminService;

    // =====================================================
    // PROPERTY MANAGEMENT
    // =====================================================

    @GetMapping("/properties")
    @Operation(summary = "Get all properties with filters", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Page<PropertyResponse>> getAllProperties(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) Property.PropertyStatus status,
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String district,
            @RequestParam(required = false) Boolean isFeatured) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                   Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        
        Pageable pageable = PageRequest.of(page, size, sort);
        Page<PropertyResponse> properties = adminService.getAllPropertiesWithFilters(
            pageable, status, city, district, isFeatured);
        return ResponseEntity.ok(properties);
    }

    @GetMapping("/properties/pending")
    @Operation(summary = "Get pending properties for approval", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Page<PropertyResponse>> getPendingProperties(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").ascending());
        Page<PropertyResponse> properties = adminService.getPendingProperties(pageable);
        return ResponseEntity.ok(properties);
    }

    @PostMapping("/properties/{propertyId}/approve")
    @Operation(summary = "Approve property", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Map<String, Object>> approveProperty(
            @PathVariable Long propertyId,
            @RequestParam(required = false) Boolean isFeatured,
            @RequestParam(required = false) String adminNote) {
        
        adminService.approveProperty(propertyId, isFeatured, adminNote);
        return ResponseEntity.ok(Map.of(
            "success", true,
            "message", "Property approved successfully",
            "propertyId", propertyId,
            "isFeatured", isFeatured != null ? isFeatured : false
        ));
    }

    @PostMapping("/properties/{propertyId}/reject")
    @Operation(summary = "Reject property", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Map<String, Object>> rejectProperty(
            @PathVariable Long propertyId,
            @RequestParam String reason) {
        
        adminService.rejectProperty(propertyId, reason);
        return ResponseEntity.ok(Map.of(
            "success", true,
            "message", "Property rejected successfully",
            "propertyId", propertyId,
            "reason", reason
        ));
    }

    @PostMapping("/properties/{propertyId}/feature")
    @Operation(summary = "Toggle property featured status", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Map<String, Object>> toggleFeatureProperty(
            @PathVariable Long propertyId,
            @RequestParam Boolean isFeatured) {
        
        adminService.toggleFeatureProperty(propertyId, isFeatured);
        return ResponseEntity.ok(Map.of(
            "success", true,
            "message", "Property feature status updated",
            "propertyId", propertyId,
            "isFeatured", isFeatured
        ));
    }

    @DeleteMapping("/properties/{propertyId}")
    @Operation(summary = "Delete property (admin force delete)", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Map<String, Object>> deleteProperty(
            @PathVariable Long propertyId,
            @RequestParam String reason) {
        
        adminService.deleteProperty(propertyId, reason);
        return ResponseEntity.ok(Map.of(
            "success", true,
            "message", "Property deleted successfully",
            "propertyId", propertyId,
            "reason", reason
        ));
    }

    // =====================================================
    // USER MANAGEMENT
    // =====================================================

    @GetMapping("/users")
    @Operation(summary = "Get all users with filters", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Page<UserManagementResponse>> getAllUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) User.UserStatus status,
            @RequestParam(required = false) User.Role role,
            @RequestParam(required = false) String search) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                   Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        
        Pageable pageable = PageRequest.of(page, size, sort);
        Page<UserManagementResponse> users = adminService.getAllUsersWithFilters(
            pageable, status, role, search);
        return ResponseEntity.ok(users);
    }

    @PostMapping("/users/{userId}/ban")
    @Operation(summary = "Ban user", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Map<String, Object>> banUser(
            @PathVariable Long userId,
            @RequestParam String reason,
            @RequestParam(required = false) Integer durationDays) {
        
        adminService.banUser(userId, reason, durationDays);
        return ResponseEntity.ok(Map.of(
            "success", true,
            "message", "User banned successfully",
            "userId", userId,
            "reason", reason,
            "durationDays", durationDays
        ));
    }

    @PostMapping("/users/{userId}/unban")
    @Operation(summary = "Unban user", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Map<String, Object>> unbanUser(
            @PathVariable Long userId,
            @RequestParam String reason) {
        
        adminService.unbanUser(userId, reason);
        return ResponseEntity.ok(Map.of(
            "success", true,
            "message", "User unbanned successfully",
            "userId", userId,
            "reason", reason
        ));
    }

    @PostMapping("/users/{userId}/role")
    @Operation(summary = "Change user role", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Map<String, Object>> changeUserRole(
            @PathVariable Long userId,
            @RequestParam User.Role newRole,
            @RequestParam String reason) {
        
        adminService.changeUserRole(userId, newRole, reason);
        return ResponseEntity.ok(Map.of(
            "success", true,
            "message", "User role changed successfully",
            "userId", userId,
            "newRole", newRole,
            "reason", reason
        ));
    }

    @GetMapping("/users/{userId}/details")
    @Operation(summary = "Get detailed user information", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<UserManagementResponse> getUserDetails(@PathVariable Long userId) {
        UserManagementResponse user = adminService.getUserDetails(userId);
        return ResponseEntity.ok(user);
    }

    // =====================================================
    // ANALYTICS & STATISTICS
    // =====================================================

    @GetMapping("/stats/dashboard")
    @Operation(summary = "Get admin dashboard statistics", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<AdminStatsResponse> getDashboardStats(
            @RequestParam(required = false) @Parameter(description = "Days to look back, default 30") Integer days) {
        
        AdminStatsResponse stats = adminService.getDashboardStats(days != null ? days : 30);
        return ResponseEntity.ok(stats);
    }

    @GetMapping("/stats/revenue")
    @Operation(summary = "Get revenue statistics", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Map<String, Object>> getRevenueStats(
            @RequestParam(required = false) LocalDateTime startDate,
            @RequestParam(required = false) LocalDateTime endDate,
            @RequestParam(defaultValue = "MONTHLY") String period) {
        
        Map<String, Object> revenueStats = adminService.getRevenueStats(startDate, endDate, period);
        return ResponseEntity.ok(revenueStats);
    }

    @GetMapping("/stats/memberships")
    @Operation(summary = "Get membership statistics", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Map<String, Object>> getMembershipStats() {
        Map<String, Object> membershipStats = adminService.getMembershipStats();
        return ResponseEntity.ok(membershipStats);
    }

    @GetMapping("/stats/properties")
    @Operation(summary = "Get property statistics", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Map<String, Object>> getPropertyStats(
            @RequestParam(required = false) Integer days) {
        
        Map<String, Object> propertyStats = adminService.getPropertyStats(days != null ? days : 30);
        return ResponseEntity.ok(propertyStats);
    }

    @GetMapping("/stats/users")
    @Operation(summary = "Get user statistics", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Map<String, Object>> getUserStats(
            @RequestParam(required = false) Integer days) {
        
        Map<String, Object> userStats = adminService.getUserStats(days != null ? days : 30);
        return ResponseEntity.ok(userStats);
    }

    // =====================================================
    // SYSTEM MANAGEMENT
    // =====================================================

    @GetMapping("/system/health")
    @Operation(summary = "Get system health status", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Map<String, Object>> getSystemHealth() {
        Map<String, Object> healthStatus = adminService.getSystemHealth();
        return ResponseEntity.ok(healthStatus);
    }

    @PostMapping("/system/maintenance")
    @Operation(summary = "Toggle maintenance mode", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Map<String, Object>> toggleMaintenanceMode(
            @RequestParam Boolean enabled,
            @RequestParam(required = false) String message) {
        
        adminService.toggleMaintenanceMode(enabled, message);
        return ResponseEntity.ok(Map.of(
            "success", true,
            "maintenanceMode", enabled,
            "message", message != null ? message : "Maintenance mode toggled"
        ));
    }

    @GetMapping("/logs/recent")
    @Operation(summary = "Get recent system logs", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Map<String, Object>> getRecentLogs(
            @RequestParam(defaultValue = "100") int limit,
            @RequestParam(defaultValue = "INFO") String level) {
        
        Map<String, Object> logs = adminService.getRecentLogs(limit, level);
        return ResponseEntity.ok(logs);
    }
} 