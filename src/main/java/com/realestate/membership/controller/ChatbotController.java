package com.realestate.membership.controller;

import com.realestate.membership.entity.ChatSession;
import com.realestate.membership.service.ChatbotService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/chatbot")
@RequiredArgsConstructor
@Tag(name = "🤖 AI Chatbot", description = "AI-powered property search and assistance")
@Slf4j
public class ChatbotController {

    private final ChatbotService chatbotService;
    private final com.realestate.membership.repository.UserRepository userRepository;

    @PostMapping("/chat")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    @Operation(summary = "Send message to AI chatbot")
    public ResponseEntity<Map<String, Object>> sendMessage(
            @Valid @RequestBody ChatRequest request,
            Authentication authentication) {
        
        try {
            Long userId = getUserId(authentication);
            
            String response = chatbotService.processMessage(
                userId, 
                request.getMessage(), 
                request.getSessionId()
            );
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "response", response,
                "timestamp", System.currentTimeMillis()
            ));
            
        } catch (Exception e) {
            log.error("Error processing chat message", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "error", "Failed to process message"
            ));
        }
    }

    @PostMapping("/sessions")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    @Operation(summary = "Create new chat session")
    public ResponseEntity<Map<String, Object>> createSession(
            @RequestBody(required = false) Map<String, String> request,
            Authentication authentication) {
        
        try {
            Long userId = getUserId(authentication);
            String title = request != null ? request.get("title") : null;
            
            ChatSession session = chatbotService.createNewSession(userId, title);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "sessionId", session.getId(),
                "title", session.getTitle(),
                "createdAt", session.getCreatedAt()
            ));
            
        } catch (Exception e) {
            log.error("Error creating chat session", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "error", "Failed to create session"
            ));
        }
    }

    @GetMapping("/sessions")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    @Operation(summary = "Get user's chat sessions")
    public ResponseEntity<Map<String, Object>> getUserSessions(Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            
            List<ChatSession> sessions = chatbotService.getUserSessions(userId);
            
            List<Map<String, Object>> sessionData = sessions.stream()
                .map(session -> {
                    Map<String, Object> sessionMap = new HashMap<>();
                    sessionMap.put("id", session.getId());
                    sessionMap.put("title", session.getTitle());
                    sessionMap.put("messageCount", session.getMessageCount());
                    sessionMap.put("lastActivity", session.getLastActivityAt());
                    sessionMap.put("createdAt", session.getCreatedAt());
                    return sessionMap;
                })
                .collect(Collectors.toList());
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "sessions", sessionData
            ));
            
        } catch (Exception e) {
            log.error("Error fetching user sessions", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "error", "Failed to fetch sessions"
            ));
        }
    }

    @GetMapping("/sessions/{sessionId}/history")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    @Operation(summary = "Get conversation history")
    public ResponseEntity<Map<String, Object>> getConversationHistory(
            @PathVariable Long sessionId,
            @RequestParam(defaultValue = "50") int limit,
            Authentication authentication) {
        
        try {
            List<Map<String, Object>> history = chatbotService.getConversationHistory(sessionId, limit);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "history", history,
                "sessionId", sessionId
            ));
            
        } catch (Exception e) {
            log.error("Error fetching conversation history", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "error", "Failed to fetch conversation history"
            ));
        }
    }

    @PostMapping("/quick-search")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    @Operation(summary = "Quick property search via AI - Enhanced with property IDs")
    public ResponseEntity<Map<String, Object>> quickSearch(
            @Valid @RequestBody QuickSearchRequest request,
            Authentication authentication) {
        
        try {
            Long userId = getUserId(authentication);
            
            String searchPrompt = String.format(
                "Find properties matching: %s. Provide a summary and recommendations.",
                request.getQuery()
            );
            
            // Try enhanced method first, fallback to legacy if not available
            String response;
            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("success", true);
            responseMap.put("query", request.getQuery());
            
            try {
                // Use enhanced method if available
                ChatbotService.ChatResponse enhancedResponse = chatbotService.processMessageEnhanced(
                    userId, searchPrompt, null);
                response = enhancedResponse.getTextResponse();
                
                // Add structured property data for frontend usage
                if (enhancedResponse.getProperties() != null && !enhancedResponse.getProperties().isEmpty()) {
                    responseMap.put("properties", enhancedResponse.getProperties());
                    responseMap.put("hasPropertyResults", true);
                    responseMap.put("propertyCount", enhancedResponse.getProperties().size());
                } else {
                    responseMap.put("hasPropertyResults", false);
                    responseMap.put("propertyCount", 0);
                }
            } catch (Exception e) {
                log.warn("Enhanced method not available, falling back to legacy: {}", e.getMessage());
                // Fallback to legacy method
                response = chatbotService.processMessage(userId, searchPrompt, null);
                responseMap.put("hasPropertyResults", false);
                responseMap.put("propertyCount", 0);
            }
            
            responseMap.put("response", response);
            
            return ResponseEntity.ok(responseMap);
            
        } catch (Exception e) {
            log.error("Error processing quick search", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "error", "Failed to process search"
            ));
        }
    }

    @GetMapping("/test")
    @Operation(summary = "🧪 Test ChatBot configuration and services")
    public ResponseEntity<Map<String, Object>> testChatBot() {
        Map<String, Object> testResult = new HashMap<>();
        
        try {
            // Test basic configuration
            testResult.put("chatbotEnabled", true);
            testResult.put("maxHistory", 10);
            testResult.put("concurrentAgents", 3);
            testResult.put("confidenceThreshold", 0.5);
            
            // Test OpenAI configuration
            Map<String, Object> openAIConfig = new HashMap<>();
            openAIConfig.put("model", "gpt-4");
            openAIConfig.put("maxTokens", 2000);
            openAIConfig.put("temperature", 0.3);
            openAIConfig.put("timeout", 45);
            openAIConfig.put("status", "Mock mode - API key configured");
            testResult.put("openAI", openAIConfig);
            
            // Test property search tool capability
            Map<String, Object> toolsConfig = new HashMap<>();
            toolsConfig.put("propertySearchTool", "Available");
            toolsConfig.put("supportedSearchParams", List.of("district", "propertyType", "bedrooms", "minPrice", "maxPrice"));
            testResult.put("tools", toolsConfig);
            
            // Test Vietnamese language support
            Map<String, Object> languageConfig = new HashMap<>();
            languageConfig.put("defaultLanguage", "vi");
            languageConfig.put("defaultPrompt", "Bạn là chuyên gia tư vấn bất động sản thông minh tại Việt Nam.");
            languageConfig.put("supportedSearchTypes", List.of("APARTMENT", "HOUSE", "VILLA", "LAND"));
            testResult.put("language", languageConfig);
            
            // Mock conversation example
            Map<String, Object> mockExample = new HashMap<>();
            mockExample.put("userQuery", "Tôi muốn tìm căn hộ 2 phòng ngủ tại Quận 1 giá từ 3-5 tỷ");
            mockExample.put("intent", "PROPERTY_SEARCH");
            mockExample.put("extractedCriteria", Map.of(
                "district", "Quận 1",
                "propertyType", "APARTMENT", 
                "bedrooms", 2,
                "minPrice", 3000000000L,
                "maxPrice", 5000000000L
            ));
            mockExample.put("expectedResponse", "Tôi sẽ tìm kiếm căn hộ 2 phòng ngủ tại Quận 1 trong khoảng giá 3-5 tỷ cho bạn...");
            testResult.put("mockExample", mockExample);
            
            // Test database table status 
            Map<String, Object> dbStatus = new HashMap<>();
            dbStatus.put("chatSessions", "Table needs JSONB → VARCHAR conversion for H2");
            dbStatus.put("chatMessages", "Dependent on chat_sessions table");
            dbStatus.put("aiAgents", "Available");
            dbStatus.put("agentExecutions", "Available");
            testResult.put("database", dbStatus);
            
            testResult.put("status", "🟡 PARTIALLY_FUNCTIONAL");
            testResult.put("limitations", List.of(
                "ChatSession table incompatible with H2 (needs JSONB → VARCHAR)",
                "OpenAI API key is mock (no real AI responses)",
                "No properties in database for search testing"
            ));
            testResult.put("recommendation", "For full testing: 1) Fix database compatibility 2) Add real OpenAI key 3) Create sample properties");
            
            return ResponseEntity.ok(testResult);
            
        } catch (Exception e) {
            log.error("Error testing chatbot", e);
            testResult.put("status", "🔴 ERROR");
            testResult.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(testResult);
        }
    }

    private Long getUserId(Authentication authentication) {
        if (authentication.getPrincipal() instanceof UserDetails userDetails) {
            // Look up user by username
            String username = userDetails.getUsername();
            return userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("User not found: " + username))
                .getId();
        }
        throw new RuntimeException("Unable to extract user ID from authentication");
    }

    // DTOs
    public static class ChatRequest {
        @NotBlank(message = "Message cannot be blank")
        private String message;
        private Long sessionId;

        // Getters and setters
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public Long getSessionId() { return sessionId; }
        public void setSessionId(Long sessionId) { this.sessionId = sessionId; }
    }

    public static class QuickSearchRequest {
        @NotBlank(message = "Search query cannot be blank")
        private String query;

        // Getters and setters
        public String getQuery() { return query; }
        public void setQuery(String query) { this.query = query; }
    }
    
    public static class PropertySearchRequest {
        private String district;
        private String propertyType;
        private Integer bedrooms;
        private Long minPrice;
        private Long maxPrice;
        
        // Getters and setters
        public String getDistrict() { return district; }
        public void setDistrict(String district) { this.district = district; }
        public String getPropertyType() { return propertyType; }
        public void setPropertyType(String propertyType) { this.propertyType = propertyType; }
        public Integer getBedrooms() { return bedrooms; }
        public void setBedrooms(Integer bedrooms) { this.bedrooms = bedrooms; }
        public Long getMinPrice() { return minPrice; }
        public void setMinPrice(Long minPrice) { this.minPrice = minPrice; }
        public Long getMaxPrice() { return maxPrice; }
        public void setMaxPrice(Long maxPrice) { this.maxPrice = maxPrice; }
    }
}