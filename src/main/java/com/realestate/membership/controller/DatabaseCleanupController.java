package com.realestate.membership.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/cleanup")
@RequiredArgsConstructor
@Slf4j
public class DatabaseCleanupController {

    @Autowired
    private DataSource dataSource;

    @PostMapping("/database")
    public ResponseEntity<Map<String, Object>> cleanupDatabase() {
        Map<String, Object> response = new HashMap<>();
        
        try (Connection connection = dataSource.getConnection();
             Statement statement = connection.createStatement()) {
            
            log.info("🗑️ Starting database cleanup...");
            
            // Disable foreign key checks
            statement.execute("SET session_replication_role = replica");
            
            // Drop tables in reverse dependency order
            String[] tables = {
                "admin_actions",
                "property_boosts", 
                "property_images",
                "payments",
                "user_memberships",
                "properties",
                "categories",
                "memberships", 
                "users",
                "ai_agents"
            };
            
            int droppedTables = 0;
            for (String table : tables) {
                try {
                    statement.execute("DROP TABLE IF EXISTS " + table + " CASCADE");
                    log.info("✅ Dropped table: {}", table);
                    droppedTables++;
                } catch (Exception e) {
                    log.warn("⚠️ Failed to drop table {}: {}", table, e.getMessage());
                }
            }
            
            // Drop sequences
            String[] sequences = {
                "users_id_seq",
                "memberships_id_seq", 
                "user_memberships_id_seq",
                "categories_id_seq",
                "properties_id_seq",
                "property_images_id_seq",
                "payments_id_seq",
                "property_boosts_id_seq",
                "admin_actions_id_seq",
                "ai_agents_id_seq"
            };
            
            int droppedSequences = 0;
            for (String sequence : sequences) {
                try {
                    statement.execute("DROP SEQUENCE IF EXISTS " + sequence + " CASCADE");
                    log.info("✅ Dropped sequence: {}", sequence);
                    droppedSequences++;
                } catch (Exception e) {
                    log.warn("⚠️ Failed to drop sequence {}: {}", sequence, e.getMessage());
                }
            }
            
            // Re-enable foreign key checks
            statement.execute("SET session_replication_role = DEFAULT");
            
            log.info("🎉 Database cleanup completed!");
            
            response.put("success", true);
            response.put("message", "Database cleanup completed successfully");
            response.put("droppedTables", droppedTables);
            response.put("droppedSequences", droppedSequences);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("❌ Database cleanup failed: {}", e.getMessage(), e);
            
            response.put("success", false);
            response.put("message", "Database cleanup failed: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.status(500).body(response);
        }
    }
    
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getDatabaseStatus() {
        Map<String, Object> response = new HashMap<>();
        
        try (Connection connection = dataSource.getConnection();
             Statement statement = connection.createStatement()) {
            
            // Check existing tables
            var rs = statement.executeQuery(
                "SELECT tablename FROM pg_tables WHERE schemaname = 'public'"
            );
            
            int tableCount = 0;
            while (rs.next()) {
                tableCount++;
            }
            
            // Check existing sequences  
            var rs2 = statement.executeQuery(
                "SELECT sequence_name FROM information_schema.sequences WHERE sequence_schema = 'public'"
            );
            
            int sequenceCount = 0;
            while (rs2.next()) {
                sequenceCount++;
            }
            
            response.put("success", true);
            response.put("tableCount", tableCount);
            response.put("sequenceCount", sequenceCount);
            response.put("isEmpty", tableCount == 0 && sequenceCount == 0);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("❌ Failed to get database status: {}", e.getMessage(), e);
            
            response.put("success", false);
            response.put("message", "Failed to get database status: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
            
            return ResponseEntity.status(500).body(response);
        }
    }
}
