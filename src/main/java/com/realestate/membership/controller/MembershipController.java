package com.realestate.membership.controller;

import com.realestate.membership.dto.MembershipResponse;
import com.realestate.membership.entity.User;
import com.realestate.membership.entity.UserMembership;
import com.realestate.membership.service.MembershipService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/memberships")
@RequiredArgsConstructor
@Tag(name = "👤 Memberships", description = "Subscription plans and user membership management")
public class MembershipController {
    
    private final MembershipService membershipService;
    
    @GetMapping
    @Operation(summary = "Get all active memberships")
    public ResponseEntity<List<MembershipResponse>> getAllMemberships() {
        List<MembershipResponse> memberships = membershipService.getAllActiveMemberships();
        return ResponseEntity.ok(memberships);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "Get membership by ID")
    public ResponseEntity<MembershipResponse> getMembershipById(@PathVariable Long id) {
        MembershipResponse membership = membershipService.getMembershipById(id);
        return ResponseEntity.ok(membership);
    }
    
    @PostMapping("/{membershipId}/subscribe")
    @Operation(summary = "Subscribe to a membership plan", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<UserMembership> subscribeMembership(
            @PathVariable Long membershipId,
            @AuthenticationPrincipal User currentUser) {
        UserMembership userMembership = membershipService.subscribeMembership(currentUser.getId(), membershipId);
        return ResponseEntity.ok(userMembership);
    }
    
    @GetMapping("/my-membership")
    @Operation(summary = "Get current user's active membership", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<UserMembership> getMyActiveMembership(@AuthenticationPrincipal User currentUser) {
        Optional<UserMembership> membership = membershipService.getUserActiveMembership(currentUser.getId());
        return membership.map(ResponseEntity::ok)
                        .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping("/my-history")
    @Operation(summary = "Get current user's membership history", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<List<UserMembership>> getMyMembershipHistory(@AuthenticationPrincipal User currentUser) {
        List<UserMembership> history = membershipService.getUserMembershipHistory(currentUser.getId());
        return ResponseEntity.ok(history);
    }
}
