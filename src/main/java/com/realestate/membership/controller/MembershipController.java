package com.realestate.membership.controller;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.realestate.membership.dto.MembershipResponse;
import com.realestate.membership.dto.PaymentRequest;
import com.realestate.membership.dto.PaymentResponse;
import com.realestate.membership.entity.Payment;
import com.realestate.membership.entity.User;
import com.realestate.membership.entity.UserMembership;
import com.realestate.membership.service.MembershipService;
import com.realestate.membership.service.PaymentService;
import com.realestate.membership.service.StripeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/memberships")
@RequiredArgsConstructor
@Tag(name = "👤 Memberships", description = "Subscription plans and user membership management")
public class MembershipController {

    private final MembershipService membershipService;
    private final PaymentService paymentService;
    private final StripeService stripeService;
    
    @GetMapping
    @Operation(summary = "Get all active memberships")
    public ResponseEntity<List<MembershipResponse>> getAllMemberships() {
        List<MembershipResponse> memberships = membershipService.getAllActiveMemberships();
        return ResponseEntity.ok(memberships);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "Get membership by ID")
    public ResponseEntity<MembershipResponse> getMembershipById(@PathVariable Long id) {
        MembershipResponse membership = membershipService.getMembershipById(id);
        return ResponseEntity.ok(membership);
    }
    
    @PostMapping("/{membershipId}/subscribe")
    @Operation(summary = "Subscribe to a membership plan", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<UserMembership> subscribeMembership(
            @PathVariable Long membershipId,
            @AuthenticationPrincipal User currentUser) {
        UserMembership userMembership = membershipService.subscribeMembership(currentUser.getId(), membershipId);
        return ResponseEntity.ok(userMembership);
    }
    
    @GetMapping("/my-membership")
    @Operation(summary = "Get current user's active membership", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<UserMembership> getMyActiveMembership(@AuthenticationPrincipal User currentUser) {
        Optional<UserMembership> membership = membershipService.getUserActiveMembership(currentUser.getId());
        return membership.map(ResponseEntity::ok)
                        .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping("/my-history")
    @Operation(summary = "Get current user's membership history", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<List<UserMembership>> getMyMembershipHistory(@AuthenticationPrincipal User currentUser) {
        List<UserMembership> history = membershipService.getUserMembershipHistory(currentUser.getId());
        return ResponseEntity.ok(history);
    }

    @PostMapping("/purchase")
    @Operation(summary = "Purchase membership with payment",
               description = "Create payment and purchase membership in one step",
               security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Map<String, Object>> purchaseMembership(
            @Valid @RequestBody PurchaseRequest request,
            @AuthenticationPrincipal User currentUser,
            HttpServletRequest httpRequest) {

        // Get membership details
        MembershipResponse membership = membershipService.getMembershipById(request.getMembershipId());

        // Create payment request
        PaymentRequest paymentRequest = new PaymentRequest();
        paymentRequest.setMembershipId(request.getMembershipId());
        paymentRequest.setAmount(membership.getPrice());
        paymentRequest.setPaymentMethod(request.getPaymentMethod());
        paymentRequest.setDescription("Purchase " + membership.getName() + " membership");

        // Create payment based on method
        Map<String, Object> result;
        switch (request.getPaymentMethod()) {
            case STRIPE:
                if ("checkout".equals(request.getStripeMode())) {
                    result = stripeService.createCheckoutSession(paymentRequest, httpRequest);
                } else {
                    result = stripeService.createPaymentIntent(paymentRequest, httpRequest);
                }
                break;
            default:
                // Create basic payment record
                PaymentResponse payment = paymentService.createPayment(paymentRequest, httpRequest);
                result = Map.of(
                    "success", true,
                    "paymentId", payment.getId(),
                    "orderId", payment.getOrderId(),
                    "amount", payment.getAmount(),
                    "status", payment.getStatus(),
                    "message", "Payment created successfully. Please complete payment to activate membership."
                );
        }

        return ResponseEntity.ok(result);
    }

    // DTO for purchase request
    @Schema(description = "Request for purchasing membership")
    public static class PurchaseRequest {

        @JsonProperty("membershipId")
        @Schema(description = "Membership ID to purchase", example = "2")
        private Long membershipId;

        @JsonProperty("paymentMethod")
        @Schema(description = "Payment method", example = "STRIPE")
        private Payment.PaymentMethod paymentMethod = Payment.PaymentMethod.STRIPE;

        @JsonProperty("stripeMode")
        @Schema(description = "Stripe mode: 'checkout' or 'intent'", example = "checkout")
        private String stripeMode = "checkout";

        public Long getMembershipId() { return membershipId; }
        public void setMembershipId(Long membershipId) { this.membershipId = membershipId; }

        public Payment.PaymentMethod getPaymentMethod() { return paymentMethod; }
        public void setPaymentMethod(Payment.PaymentMethod paymentMethod) { this.paymentMethod = paymentMethod; }

        public String getStripeMode() { return stripeMode; }
        public void setStripeMode(String stripeMode) { this.stripeMode = stripeMode; }
    }
}
