package com.realestate.membership.controller;

import com.realestate.membership.dto.NotificationResponse;
import com.realestate.membership.entity.Notification;
import com.realestate.membership.service.NotificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/notifications")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "🔔 Notifications", description = "Real-time notification system with multi-channel delivery")
public class NotificationController {

    private final NotificationService notificationService;

    // =====================================================
    // USER NOTIFICATION ENDPOINTS
    // =====================================================

    @GetMapping
    @Operation(summary = "Get user notifications", security = @SecurityRequirement(name = "bearerAuth"))
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<Page<NotificationResponse>> getNotifications(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                   Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        
        Pageable pageable = PageRequest.of(page, size, sort);
        Page<Notification> notifications = notificationService.getUserNotifications(pageable);
        
        Page<NotificationResponse> response = notifications.map(this::mapToNotificationResponse);
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/unread")
    @Operation(summary = "Get unread notifications", security = @SecurityRequirement(name = "bearerAuth"))
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<List<NotificationResponse>> getUnreadNotifications() {
        List<Notification> notifications = notificationService.getUnreadNotifications();
        
        List<NotificationResponse> response = notifications.stream()
            .map(this::mapToNotificationResponse)
            .collect(Collectors.toList());
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/unread/count")
    @Operation(summary = "Get unread notification count", security = @SecurityRequirement(name = "bearerAuth"))
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getUnreadCount() {
        Long count = notificationService.getUnreadCount();
        
        return ResponseEntity.ok(Map.of(
            "unreadCount", count,
            "hasUnread", count > 0
        ));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get notification by ID", security = @SecurityRequirement(name = "bearerAuth"))
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<NotificationResponse> getNotificationById(@PathVariable Long id) {
        return notificationService.getNotificationById(id)
            .map(this::mapToNotificationResponse)
            .map(ResponseEntity::ok)
            .orElse(ResponseEntity.notFound().build());
    }

    // =====================================================
    // NOTIFICATION ACTIONS
    // =====================================================

    @PutMapping("/{id}/read")
    @Operation(summary = "Mark notification as read", security = @SecurityRequirement(name = "bearerAuth"))
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> markAsRead(@PathVariable Long id) {
        try {
            notificationService.markAsRead(id);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Notification marked as read",
                "notificationId", id
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to mark notification as read",
                "error", e.getMessage()
            ));
        }
    }

    @PutMapping("/read-all")
    @Operation(summary = "Mark all notifications as read", security = @SecurityRequirement(name = "bearerAuth"))
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> markAllAsRead() {
        try {
            notificationService.markAllAsRead();
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "All notifications marked as read"
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to mark all notifications as read",
                "error", e.getMessage()
            ));
        }
    }

    @PutMapping("/{id}/archive")
    @Operation(summary = "Archive notification", security = @SecurityRequirement(name = "bearerAuth"))
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> archiveNotification(@PathVariable Long id) {
        try {
            notificationService.markAsArchived(id);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Notification archived",
                "notificationId", id
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to archive notification",
                "error", e.getMessage()
            ));
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete notification", security = @SecurityRequirement(name = "bearerAuth"))
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> deleteNotification(@PathVariable Long id) {
        try {
            notificationService.deleteNotification(id);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Notification deleted",
                "notificationId", id
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to delete notification",
                "error", e.getMessage()
            ));
        }
    }

    // =====================================================
    // HELPER METHODS
    // =====================================================

    private NotificationResponse mapToNotificationResponse(Notification notification) {
        NotificationResponse response = new NotificationResponse();
        response.setId(notification.getId());
        response.setTitle(notification.getTitle());
        response.setMessage(notification.getMessage());
        response.setType(notification.getType().toString());
        response.setPriority(notification.getPriority().toString());
        response.setStatus(notification.getStatus().toString());
        response.setRead(notification.isRead());
        response.setActionUrl(notification.getActionUrl());
        response.setActionText(notification.getActionText());
        response.setPropertyId(notification.getPropertyId());
        response.setMembershipId(notification.getMembershipId());
        response.setPaymentId(notification.getPaymentId());
        response.setCreatedAt(notification.getCreatedAt());
        response.setReadAt(notification.getReadAt());
        response.setExpiresAt(notification.getExpiresAt());
        
        // Set display properties
        response.setDisplayType(getDisplayType(notification.getType()));
        response.setDisplayPriority(getDisplayPriority(notification.getPriority()));
        response.setPriorityColor(getPriorityColor(notification.getPriority()));
        response.setTypeIcon(getTypeIcon(notification.getType()));
        
        return response;
    }

    private String getDisplayType(Notification.NotificationType type) {
        return switch (type) {
            case MEMBERSHIP_EXPIRY_WARNING -> "Cảnh báo hết hạn";
            case MEMBERSHIP_EXPIRED -> "Hết hạn membership";
            case MEMBERSHIP_ACTIVATED -> "Kích hoạt membership";
            case PROPERTY_APPROVED -> "Tin đăng được duyệt";
            case PROPERTY_REJECTED -> "Tin đăng bị từ chối";
            case PROPERTY_CONTACT_INQUIRY -> "Liên hệ tin đăng";
            case PAYMENT_SUCCESS -> "Thanh toán thành công";
            case PAYMENT_FAILED -> "Thanh toán thất bại";
            case WELCOME -> "Chào mừng";
            case SYSTEM_ANNOUNCEMENT -> "Thông báo hệ thống";
            default -> type.toString();
        };
    }

    private String getDisplayPriority(Notification.NotificationPriority priority) {
        return switch (priority) {
            case LOW -> "Thấp";
            case NORMAL -> "Bình thường";
            case HIGH -> "Cao";
            case URGENT -> "Khẩn cấp";
        };
    }

    private String getPriorityColor(Notification.NotificationPriority priority) {
        return switch (priority) {
            case LOW -> "blue";
            case NORMAL -> "green";
            case HIGH -> "orange";
            case URGENT -> "red";
        };
    }

    private String getTypeIcon(Notification.NotificationType type) {
        return switch (type) {
            case MEMBERSHIP_EXPIRY_WARNING, MEMBERSHIP_EXPIRED -> "⏰";
            case MEMBERSHIP_ACTIVATED -> "✅";
            case PROPERTY_APPROVED -> "✅";
            case PROPERTY_REJECTED -> "❌";
            case PROPERTY_CONTACT_INQUIRY -> "📞";
            case PAYMENT_SUCCESS -> "💰";
            case PAYMENT_FAILED -> "❌";
            case WELCOME -> "👋";
            case SYSTEM_ANNOUNCEMENT -> "📢";
            default -> "🔔";
        };
    }
} 