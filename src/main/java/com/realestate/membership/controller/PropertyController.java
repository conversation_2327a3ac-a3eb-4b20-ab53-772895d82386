package com.realestate.membership.controller;

import com.realestate.membership.dto.PropertyRequest;
import com.realestate.membership.dto.PropertyResponse;
import com.realestate.membership.entity.Property;
import com.realestate.membership.entity.User;
import com.realestate.membership.service.PropertyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

@RestController
@RequestMapping("/properties")
@RequiredArgsConstructor
@Tag(name = "🏘️ Properties", description = "Property CRUD operations, search, and image management")
public class PropertyController {
    
    private final PropertyService propertyService;
    
    @GetMapping
    @Operation(summary = "Get all approved properties")
    public ResponseEntity<Page<PropertyResponse>> getAllProperties(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                   Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        
        Pageable pageable = PageRequest.of(page, size, sort);
        Page<PropertyResponse> properties = propertyService.getAllProperties(pageable);
        return ResponseEntity.ok(properties);
    }
    
    @GetMapping("/search")
    @Operation(summary = "Search properties with filters")
    public ResponseEntity<Page<PropertyResponse>> searchProperties(
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String district,
            @RequestParam(required = false) Property.PropertyType propertyType,
            @RequestParam(required = false) Property.ListingType listingType,
            @RequestParam(required = false) BigDecimal minPrice,
            @RequestParam(required = false) BigDecimal maxPrice,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<PropertyResponse> properties = propertyService.searchProperties(
                city, district, propertyType, listingType, minPrice, maxPrice, categoryId, pageable);
        return ResponseEntity.ok(properties);
    }
    
    @GetMapping("/featured")
    @Operation(summary = "Get featured properties")
    public ResponseEntity<List<PropertyResponse>> getFeaturedProperties(
            @RequestParam(defaultValue = "10") int limit) {
        List<PropertyResponse> properties = propertyService.getFeaturedProperties(limit);
        return ResponseEntity.ok(properties);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "Get property by ID")
    public ResponseEntity<PropertyResponse> getPropertyById(@PathVariable Long id) {
        PropertyResponse property = propertyService.getPropertyById(id);
        return ResponseEntity.ok(property);
    }
    
    @PostMapping
    @Operation(summary = "Create a new property", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<PropertyResponse> createProperty(
            @Valid @RequestBody PropertyRequest request,
            @AuthenticationPrincipal User currentUser) {
        PropertyResponse property = propertyService.createProperty(currentUser.getId(), request);
        return ResponseEntity.ok(property);
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "Update property", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<PropertyResponse> updateProperty(
            @PathVariable Long id,
            @Valid @RequestBody PropertyRequest request,
            @AuthenticationPrincipal User currentUser) {
        PropertyResponse property = propertyService.updateProperty(currentUser.getId(), id, request);
        return ResponseEntity.ok(property);
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete property", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Void> deleteProperty(
            @PathVariable Long id,
            @AuthenticationPrincipal User currentUser) {
        propertyService.deleteProperty(currentUser.getId(), id);
        return ResponseEntity.noContent().build();
    }
    
    @GetMapping("/my-properties")
    @Operation(summary = "Get current user's properties", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Page<PropertyResponse>> getMyProperties(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @AuthenticationPrincipal User currentUser) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<PropertyResponse> properties = propertyService.getUserProperties(currentUser.getId(), pageable);
        return ResponseEntity.ok(properties);
    }
}
