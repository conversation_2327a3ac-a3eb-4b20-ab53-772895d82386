package com.realestate.membership.dto;

import com.realestate.membership.entity.Membership;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Membership plan information")
public class MembershipResponse {

    @Schema(description = "Membership ID", example = "1")
    private Long id;

    @Schema(description = "Membership name", example = "Advanced")
    private String name;

    @Schema(description = "Membership description", example = "Gói nâng cao với AI Generation và Push Top")
    private String description;

    @Schema(description = "Monthly price in VND", example = "299000")
    private BigDecimal price;

    @Schema(description = "Duration in months", example = "1")
    private Integer durationMonths;

    @Schema(description = "Maximum properties allowed", example = "50")
    private Integer maxProperties;

    @Schema(description = "Number of featured properties", example = "10")
    private Integer featuredProperties;

    @Schema(description = "Priority support included", example = "true")
    private Boolean prioritySupport;

    @Schema(description = "Analytics access included", example = "true")
    private Boolean analyticsAccess;

    @Schema(description = "Multiple images allowed", example = "true")
    private Boolean multipleImages;

    @Schema(description = "Contact info display allowed", example = "true")
    private Boolean contactInfoDisplay;

    @Schema(description = "AI content generation included", example = "true")
    private Boolean aiContentGeneration;

    @Schema(description = "Monthly push top limit", example = "10")
    private Integer pushTopLimit;

    @Schema(description = "Membership type", example = "ADVANCED")
    private Membership.MembershipType type;

    @Schema(description = "Is membership active", example = "true")
    private Boolean isActive;

    @Schema(description = "Created timestamp")
    private LocalDateTime createdAt;

    @Schema(description = "Updated timestamp")
    private LocalDateTime updatedAt;
}
