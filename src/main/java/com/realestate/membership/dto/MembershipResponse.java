package com.realestate.membership.dto;

import com.realestate.membership.entity.Membership;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MembershipResponse {
    private Long id;
    private String name;
    private String description;
    private BigDecimal price;
    private Integer durationMonths;
    private Integer maxProperties;
    private Integer featuredProperties;
    private Boolean prioritySupport;
    private Boolean analyticsAccess;
    private Boolean multipleImages;
    private Boolean contactInfoDisplay;
    private Membership.MembershipType type;
    private Boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
