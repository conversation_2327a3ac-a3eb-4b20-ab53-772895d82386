package com.realestate.membership.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class NotificationResponse {
    
    private Long id;
    private String title;
    private String message;
    private String type;
    private String priority;
    private String status;
    private Boolean read;
    
    // Action details
    private String actionUrl;
    private String actionText;
    
    // Related entities
    private Long propertyId;
    private Long membershipId;
    private Long paymentId;
    
    // Timestamps
    private LocalDateTime createdAt;
    private LocalDateTime readAt;
    private LocalDateTime expiresAt;
    
    // Display properties
    private String displayType;
    private String displayPriority;
    private String priorityColor;
    private String typeIcon;
    
    // Helper methods
    public Boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }
    
    public Boolean isUnread() {
        return !read;
    }
    
    public Boolean hasAction() {
        return actionUrl != null && !actionUrl.trim().isEmpty();
    }
    
    public String getTimeAgo() {
        if (createdAt == null) return "";
        
        LocalDateTime now = LocalDateTime.now();
        long minutes = java.time.Duration.between(createdAt, now).toMinutes();
        
        if (minutes < 1) return "Vừa xong";
        if (minutes < 60) return minutes + " phút trước";
        
        long hours = minutes / 60;
        if (hours < 24) return hours + " giờ trước";
        
        long days = hours / 24;
        if (days < 7) return days + " ngày trước";
        
        long weeks = days / 7;
        if (weeks < 4) return weeks + " tuần trước";
        
        long months = days / 30;
        return months + " tháng trước";
    }
} 