package com.realestate.membership.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.realestate.membership.entity.Payment;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaymentRequest {
    
    @NotNull(message = "Membership ID is required")
    @Positive(message = "Membership ID must be positive")
    @JsonProperty("membershipId")
    private Long membershipId;
    
    @NotNull(message = "Payment method is required")
    @JsonProperty("paymentMethod")
    private Payment.PaymentMethod paymentMethod;
    
    @NotNull(message = "Amount is required")
    @DecimalMin(value = "1000.0", message = "Minimum payment amount is 1,000 VND")
    @DecimalMax(value = "*********.0", message = "Maximum payment amount is 100,000,000 VND")
    @JsonProperty("amount")
    private BigDecimal amount;
    
    @Size(max = 500, message = "Description cannot exceed 500 characters")
    @JsonProperty("description")
    private String description;
    
    @Size(max = 100, message = "Order info cannot exceed 100 characters")
    @JsonProperty("orderInfo")
    private String orderInfo;
    
    // VNPay specific fields
    @Size(max = 2, message = "Bank code cannot exceed 2 characters")
    @JsonProperty("bankCode")
    private String bankCode; // NCB, VNPAYQR, etc.
    
    @Size(max = 20, message = "Locale cannot exceed 20 characters")
    @JsonProperty("locale")
    private String locale = "vn"; // vn, en
    
    // MoMo specific fields
    @Size(max = 50, message = "Request type cannot exceed 50 characters")
    @JsonProperty("requestType")
    private String requestType = "captureWallet"; // captureWallet, payWithATM
    
    @Size(max = 200, message = "Extra data cannot exceed 200 characters")
    @JsonProperty("extraData")
    private String extraData;
    
    // Auto renewal settings
    @JsonProperty("autoRenewal")
    private Boolean autoRenewal = false;
    
    // Client information
    @Size(max = 45, message = "IP address cannot exceed 45 characters")
    @JsonProperty("clientIp")
    private String clientIp;
    
    @Size(max = 500, message = "User agent cannot exceed 500 characters")
    @JsonProperty("userAgent")
    private String userAgent;
    
    // Return URLs
    @Size(max = 500, message = "Return URL cannot exceed 500 characters")
    @JsonProperty("returnUrl")
    private String returnUrl;
    
    @Size(max = 500, message = "Cancel URL cannot exceed 500 characters")
    @JsonProperty("cancelUrl")
    private String cancelUrl;
    
    // Helper method to validate payment method compatibility
    public boolean isValidForVNPay() {
        return paymentMethod == Payment.PaymentMethod.VNPAY || 
               paymentMethod == Payment.PaymentMethod.BANK_TRANSFER;
    }
    
    public boolean isValidForMoMo() {
        return paymentMethod == Payment.PaymentMethod.MOMO || 
               paymentMethod == Payment.PaymentMethod.WALLET;
    }
    
    public boolean isValidForStripe() {
        return paymentMethod == Payment.PaymentMethod.STRIPE &&
               amount != null && amount.compareTo(BigDecimal.valueOf(500)) >= 0 && // Min 500 VND = ~0.02 USD
               amount.compareTo(BigDecimal.valueOf(*********)) <= 0; // Max 100M VND = ~4,200 USD
    }
    
    // Default values setter
    public void setDefaultValues() {
        if (description == null || description.trim().isEmpty()) {
            description = "Thanh toán gói membership";
        }
        
        if (orderInfo == null || orderInfo.trim().isEmpty()) {
            orderInfo = "Membership Payment ID: " + System.currentTimeMillis();
        }
        
        if (locale == null) {
            locale = "vn";
        }
        
        if (requestType == null) {
            requestType = "captureWallet";
        }
    }
} 