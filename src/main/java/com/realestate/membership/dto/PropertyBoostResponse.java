package com.realestate.membership.dto;

import com.realestate.membership.entity.PropertyBoost;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Property boost information")
public class PropertyBoostResponse {
    
    @Schema(description = "Boost ID", example = "1")
    private Long id;
    
    @Schema(description = "Property ID", example = "123")
    private Long propertyId;
    
    @Schema(description = "Property title", example = "Beautiful Apartment in District 1")
    private String propertyTitle;
    
    @Schema(description = "User ID", example = "456")
    private Long userId;
    
    @Schema(description = "Boost type", example = "PUSH_TOP")
    private PropertyBoost.BoostType boostType;
    
    @Schema(description = "Boost start time")
    private LocalDateTime boostStart;
    
    @Schema(description = "Boost end time")
    private LocalDateTime boostEnd;
    
    @Schema(description = "Is boost currently active", example = "true")
    private Boolean isActive;
    
    @Schema(description = "Is boost currently effective", example = "true")
    private Boolean isCurrentlyActive;
    
    @Schema(description = "Days remaining", example = "5")
    private Long daysRemaining;
    
    @Schema(description = "Created at")
    private LocalDateTime createdAt;
    
    @Schema(description = "Updated at")
    private LocalDateTime updatedAt;
}
