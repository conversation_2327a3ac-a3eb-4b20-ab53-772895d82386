package com.realestate.membership.dto;

import com.realestate.membership.entity.Property;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PropertyResponse {
    private Long id;
    private String title;
    private String description;
    private BigDecimal price;
    private String address;
    private String city;
    private String district;
    private String ward;
    private BigDecimal latitude;
    private BigDecimal longitude;
    private BigDecimal propertyArea;
    private BigDecimal landArea;
    private Integer bedrooms;
    private Integer bathrooms;
    private Integer floors;
    private Property.PropertyType propertyType;
    private Property.ListingType listingType;
    private Property.PropertyStatus status;
    private Boolean isFeatured;
    private Boolean boosted = false;
    private Long viewCount;
    private Long contactCount;
    private LocalDateTime publishedAt;
    private LocalDateTime expiresAt;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // User info
    private String ownerName;
    private String ownerPhone;
    private String ownerEmail;
    
    // Category info
    private String categoryName;
    
    // Images
    private List<PropertyImageResponse> images;
}
