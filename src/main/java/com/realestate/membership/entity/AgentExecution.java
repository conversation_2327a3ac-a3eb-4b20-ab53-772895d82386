package com.realestate.membership.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "agent_executions")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgentExecution {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "message_id", nullable = false)
    private ChatMessage message;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "agent_id", nullable = false)
    private AIAgent agent;
    
    @Column(name = "input_prompt", columnDefinition = "TEXT", nullable = false)
    private String inputPrompt;
    
    @Column(name = "raw_response", columnDefinition = "TEXT")
    private String rawResponse;
    
    @Column(name = "processed_response", columnDefinition = "TEXT")
    private String processedResponse;
    
    @Column(name = "confidence_score", precision = 4, scale = 3)
    private BigDecimal confidenceScore;
    
    @Column(name = "execution_time")
    private Integer executionTime; // milliseconds
    
    @Column(name = "token_usage")
    private Integer tokenUsage;
    
    @Column(precision = 8, scale = 6)
    private BigDecimal cost; // USD
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private ExecutionStatus status = ExecutionStatus.SUCCESS;
    
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    public enum ExecutionStatus {
        SUCCESS, FAILED, TIMEOUT
    }
}