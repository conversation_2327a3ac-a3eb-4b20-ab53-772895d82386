package com.realestate.membership.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.Map;

@Entity
@Table(name = "notifications")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class Notification {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // Recipient
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    // Notification details
    @Column(nullable = false)
    private String title;

    @Column(columnDefinition = "TEXT")
    private String message;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private NotificationType type;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private NotificationPriority priority = NotificationPriority.NORMAL;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private NotificationStatus status = NotificationStatus.UNREAD;

    // Channels to send notification
    @Column(name = "send_email")
    private Boolean sendEmail = false;

    @Column(name = "send_push")
    private Boolean sendPush = false;

    @Column(name = "send_sms")
    private Boolean sendSms = false;

    // Delivery status
    @Column(name = "email_sent")
    private Boolean emailSent = false;

    @Column(name = "push_sent")
    private Boolean pushSent = false;

    @Column(name = "sms_sent")
    private Boolean smsSent = false;

    // Related entities (optional references)
    @Column(name = "property_id")
    private Long propertyId;

    @Column(name = "membership_id")
    private Long membershipId;

    @Column(name = "payment_id")
    private Long paymentId;

    // Action URLs
    @Column(name = "action_url")
    private String actionUrl;

    @Column(name = "action_text")
    private String actionText;

    // Metadata for dynamic content (stored as JSON string for H2 compatibility)
    @Column(columnDefinition = "TEXT")
    private String metadata;

    // Scheduling
    @Column(name = "scheduled_at")
    private LocalDateTime scheduledAt;

    @Column(name = "sent_at")
    private LocalDateTime sentAt;

    @Column(name = "read_at")
    private LocalDateTime readAt;

    @Column(name = "expires_at")
    private LocalDateTime expiresAt;

    // Retry mechanism
    @Column(name = "retry_count")
    private Integer retryCount = 0;

    @Column(name = "max_retries")
    private Integer maxRetries = 3;

    @Column(name = "last_error")
    private String lastError;

    // Timestamps
    @CreatedDate
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // =====================================================
    // ENUMS
    // =====================================================

    public enum NotificationType {
        // Membership related
        MEMBERSHIP_EXPIRY_WARNING,
        MEMBERSHIP_EXPIRED,
        MEMBERSHIP_ACTIVATED,
        MEMBERSHIP_CANCELLED,
        
        // Property related
        PROPERTY_APPROVED,
        PROPERTY_REJECTED,
        PROPERTY_EXPIRED,
        PROPERTY_CONTACT_INQUIRY,
        PROPERTY_FEATURED,
        
        // Payment related
        PAYMENT_SUCCESS,
        PAYMENT_FAILED,
        PAYMENT_REFUNDED,
        PAYMENT_REMINDER,
        
        // User related
        WELCOME,
        PASSWORD_RESET,
        EMAIL_VERIFICATION,
        PROFILE_UPDATED,
        
        // System related
        SYSTEM_ANNOUNCEMENT,
        SYSTEM_MAINTENANCE,
        ADMIN_MESSAGE,
        
        // Other
        CONTACT_INQUIRY,
        NEWSLETTER,
        PROMOTION
    }

    public enum NotificationPriority {
        LOW,
        NORMAL,
        HIGH,
        URGENT
    }

    public enum NotificationStatus {
        UNREAD,
        READ,
        ARCHIVED,
        DELETED
    }

    // =====================================================
    // HELPER METHODS
    // =====================================================

    public boolean isRead() {
        return status == NotificationStatus.READ;
    }

    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }

    public boolean isScheduled() {
        return scheduledAt != null && LocalDateTime.now().isBefore(scheduledAt);
    }

    public boolean canRetry() {
        return retryCount < maxRetries;
    }

    public void markAsRead() {
        this.status = NotificationStatus.READ;
        this.readAt = LocalDateTime.now();
    }

    public void markAsSent() {
        this.sentAt = LocalDateTime.now();
    }

    public void incrementRetryCount() {
        this.retryCount++;
    }

    // Factory methods for common notification types
    public static Notification membershipExpiring(User user, int daysLeft, Long membershipId) {
        Notification notification = new Notification();
        notification.setUser(user);
        notification.setType(NotificationType.MEMBERSHIP_EXPIRY_WARNING);
        notification.setPriority(NotificationPriority.HIGH);
        notification.setTitle("Membership sắp hết hạn");
        notification.setMessage(String.format("Gói membership của bạn sẽ hết hạn sau %d ngày. Gia hạn ngay để tiếp tục sử dụng dịch vụ.", daysLeft));
        notification.setMembershipId(membershipId);
        notification.setSendEmail(true);
        notification.setSendPush(true);
        notification.setActionUrl("/memberships/renew");
        notification.setActionText("Gia hạn ngay");
        notification.setExpiresAt(LocalDateTime.now().plusDays(30));
        return notification;
    }

    public static Notification propertyApproved(User user, Long propertyId, String propertyTitle) {
        Notification notification = new Notification();
        notification.setUser(user);
        notification.setType(NotificationType.PROPERTY_APPROVED);
        notification.setPriority(NotificationPriority.NORMAL);
        notification.setTitle("Tin đăng được duyệt");
        notification.setMessage(String.format("Tin đăng '%s' đã được duyệt và hiển thị công khai.", propertyTitle));
        notification.setPropertyId(propertyId);
        notification.setSendEmail(true);
        notification.setSendPush(true);
        notification.setActionUrl("/properties/" + propertyId);
        notification.setActionText("Xem tin đăng");
        notification.setExpiresAt(LocalDateTime.now().plusDays(7));
        return notification;
    }

    public static Notification paymentSuccess(User user, Long paymentId, String amount) {
        Notification notification = new Notification();
        notification.setUser(user);
        notification.setType(NotificationType.PAYMENT_SUCCESS);
        notification.setPriority(NotificationPriority.NORMAL);
        notification.setTitle("Thanh toán thành công");
        notification.setMessage(String.format("Bạn đã thanh toán thành công %s VND. Gói membership đã được kích hoạt.", amount));
        notification.setPaymentId(paymentId);
        notification.setSendEmail(true);
        notification.setSendPush(true);
        notification.setActionUrl("/payments/" + paymentId);
        notification.setActionText("Xem chi tiết");
        notification.setExpiresAt(LocalDateTime.now().plusDays(30));
        return notification;
    }

    public static Notification contactInquiry(User user, Long propertyId, String inquirerName, String propertyTitle) {
        Notification notification = new Notification();
        notification.setUser(user);
        notification.setType(NotificationType.PROPERTY_CONTACT_INQUIRY);
        notification.setPriority(NotificationPriority.HIGH);
        notification.setTitle("Có người quan tâm tin đăng");
        notification.setMessage(String.format("%s đã liên hệ về tin đăng '%s' của bạn.", inquirerName, propertyTitle));
        notification.setPropertyId(propertyId);
        notification.setSendEmail(true);
        notification.setSendPush(true);
        notification.setActionUrl("/properties/" + propertyId + "/contacts");
        notification.setActionText("Xem liên hệ");
        notification.setExpiresAt(LocalDateTime.now().plusDays(14));
        return notification;
    }
} 