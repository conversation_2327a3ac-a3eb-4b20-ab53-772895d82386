package com.realestate.membership.repository;

import com.realestate.membership.entity.AIAgent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AIAgentRepository extends JpaRepository<AIAgent, Long> {
    
    Optional<AIAgent> findByNameAndIsActiveTrue(String name);
    
    List<AIAgent> findByIsActiveTrueOrderByPriority();
    
    List<AIAgent> findByNameInAndIsActiveTrueOrderByPriority(List<String> names);
    
    List<AIAgent> findByApiProviderAndIsActiveTrue(String apiProvider);
}