package com.realestate.membership.repository;

import com.realestate.membership.entity.AgentExecution;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AgentExecutionRepository extends JpaRepository<AgentExecution, Long> {
    
    List<AgentExecution> findByAgentIdOrderByCreatedAtDesc(Long agentId);
    
    List<AgentExecution> findByMessageIdOrderByCreatedAtDesc(Long messageId);
    
    @Query("SELECT ae FROM AgentExecution ae WHERE ae.status = 'SUCCESS' " +
           "ORDER BY ae.confidenceScore DESC")
    List<AgentExecution> findSuccessfulExecutionsOrderByConfidence();
    
    @Query("SELECT COUNT(ae) FROM AgentExecution ae WHERE ae.agent.id = ?1 AND ae.status = 'SUCCESS'")
    long countSuccessfulExecutionsByAgent(Long agentId);
}