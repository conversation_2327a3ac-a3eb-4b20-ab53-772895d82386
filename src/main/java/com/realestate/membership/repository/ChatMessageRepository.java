package com.realestate.membership.repository;

import com.realestate.membership.entity.ChatMessage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ChatMessageRepository extends JpaRepository<ChatMessage, Long> {
    
    List<ChatMessage> findBySessionIdOrderByCreatedAtDesc(Long sessionId);
    
    List<ChatMessage> findBySessionIdAndMessageTypeOrderByCreatedAtDesc(
        Long sessionId, ChatMessage.MessageType messageType);
    
    long countBySessionId(Long sessionId);
}