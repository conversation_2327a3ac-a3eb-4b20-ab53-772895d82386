package com.realestate.membership.repository;

import com.realestate.membership.entity.ChatSession;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ChatSessionRepository extends JpaRepository<ChatSession, Long> {
    
    List<ChatSession> findByUserIdAndStatusOrderByLastActivityAtDesc(
        Long userId, ChatSession.SessionStatus status);
    
    List<ChatSession> findByUserIdOrderByLastActivityAtDesc(Long userId);
    
    long countByUserId(Long userId);
}