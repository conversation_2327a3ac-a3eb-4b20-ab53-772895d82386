package com.realestate.membership.repository;

import com.realestate.membership.entity.Membership;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MembershipRepository extends JpaRepository<Membership, Long> {
    
    List<Membership> findByIsActiveTrue();
    
    @Query("SELECT m FROM Membership m WHERE m.isActive = true ORDER BY m.price ASC")
    List<Membership> findActiveMembershipsByPriceAsc();
    
    Membership findByType(Membership.MembershipType type);
}
