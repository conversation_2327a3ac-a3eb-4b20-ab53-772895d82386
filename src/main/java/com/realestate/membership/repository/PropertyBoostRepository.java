package com.realestate.membership.repository;

import com.realestate.membership.entity.PropertyBoost;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface PropertyBoostRepository extends JpaRepository<PropertyBoost, Long> {
    
    // Find active boosts for a property
    @Query("SELECT pb FROM PropertyBoost pb WHERE pb.property.id = :propertyId " +
           "AND pb.isActive = true AND pb.boostStart <= :now AND pb.boostEnd > :now")
    List<PropertyBoost> findActiveBoostsByPropertyId(@Param("propertyId") Long propertyId, 
                                                    @Param("now") LocalDateTime now);
    
    // Find user's boost history
    Page<PropertyBoost> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);
    
    // Find user's active boosts
    @Query("SELECT pb FROM PropertyBoost pb WHERE pb.user.id = :userId " +
           "AND pb.isActive = true AND pb.boostStart <= :now AND pb.boostEnd > :now")
    List<PropertyBoost> findActiveBoostsByUserId(@Param("userId") Long userId, 
                                               @Param("now") LocalDateTime now);
    
    // Count user's boosts this month
    @Query("SELECT COUNT(pb) FROM PropertyBoost pb WHERE pb.user.id = :userId " +
           "AND pb.boostType = :boostType " +
           "AND pb.createdAt >= :startOfMonth AND pb.createdAt < :endOfMonth")
    Long countUserBoostsThisMonth(@Param("userId") Long userId,
                                 @Param("boostType") PropertyBoost.BoostType boostType,
                                 @Param("startOfMonth") LocalDateTime startOfMonth,
                                 @Param("endOfMonth") LocalDateTime endOfMonth);
    
    // Find currently boosted properties (for sorting)
    @Query("SELECT DISTINCT pb.property.id FROM PropertyBoost pb " +
           "WHERE pb.isActive = true AND pb.boostStart <= :now AND pb.boostEnd > :now " +
           "AND pb.boostType = :boostType")
    List<Long> findCurrentlyBoostedPropertyIds(@Param("boostType") PropertyBoost.BoostType boostType,
                                              @Param("now") LocalDateTime now);
    
    // Check if property is currently boosted
    @Query("SELECT pb FROM PropertyBoost pb WHERE pb.property.id = :propertyId " +
           "AND pb.isActive = true AND pb.boostStart <= :now AND pb.boostEnd > :now " +
           "AND pb.boostType = :boostType")
    Optional<PropertyBoost> findActiveBoostByPropertyAndType(@Param("propertyId") Long propertyId,
                                                           @Param("boostType") PropertyBoost.BoostType boostType,
                                                           @Param("now") LocalDateTime now);
}
