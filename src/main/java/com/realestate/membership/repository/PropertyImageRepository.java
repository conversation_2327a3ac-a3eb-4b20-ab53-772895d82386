package com.realestate.membership.repository;

import com.realestate.membership.entity.PropertyImage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PropertyImageRepository extends JpaRepository<PropertyImage, Long> {
    List<PropertyImage> findByPropertyIdOrderBySortOrderAsc(Long propertyId);
    List<PropertyImage> findByPropertyIdAndIsPrimaryTrue(Long propertyId);
    long countByPropertyId(Long propertyId);
} 