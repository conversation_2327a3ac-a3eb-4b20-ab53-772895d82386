package com.realestate.membership.repository;

import com.realestate.membership.entity.Property;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface PropertyRepository extends JpaRepository<Property, Long> {
    
    Page<Property> findByStatusOrderByCreatedAtDesc(Property.PropertyStatus status, Pageable pageable);
    
    Page<Property> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);
    
    @Query("SELECT p FROM Property p WHERE p.status = 'APPROVED' AND p.isFeatured = true ORDER BY p.publishedAt DESC")
    List<Property> findFeaturedProperties(Pageable pageable);
    
    @Query("SELECT p FROM Property p WHERE p.status = 'APPROVED' " +
           "AND (:city IS NULL OR p.city LIKE %:city%) " +
           "AND (:district IS NULL OR p.district LIKE %:district%) " +
           "AND (:propertyType IS NULL OR p.propertyType = :propertyType) " +
           "AND (:listingType IS NULL OR p.listingType = :listingType) " +
           "AND (:minPrice IS NULL OR p.price >= :minPrice) " +
           "AND (:maxPrice IS NULL OR p.price <= :maxPrice) " +
           "AND (:categoryId IS NULL OR p.category.id = :categoryId) " +
           "ORDER BY p.isFeatured DESC, p.publishedAt DESC")
    Page<Property> searchProperties(
            @Param("city") String city,
            @Param("district") String district,
            @Param("propertyType") Property.PropertyType propertyType,
            @Param("listingType") Property.ListingType listingType,
            @Param("minPrice") BigDecimal minPrice,
            @Param("maxPrice") BigDecimal maxPrice,
            @Param("categoryId") Long categoryId,
            Pageable pageable);
    
    @Query("SELECT COUNT(p) FROM Property p WHERE p.user.id = :userId AND p.status NOT IN ('SOLD', 'RENTED', 'EXPIRED')")
    Long countActivePropertiesByUserId(@Param("userId") Long userId);
    
    List<Property> findByUserIdAndStatus(Long userId, Property.PropertyStatus status);
    
    /**
     * Find properties by chatbot search criteria
     */
    @Query("SELECT p FROM Property p WHERE p.status = 'APPROVED' " +
           "AND (:district IS NULL OR p.district LIKE %:district%) " +
           "AND (:propertyType IS NULL OR p.propertyType = :propertyType) " +
           "AND (:bedrooms IS NULL OR p.bedrooms = :bedrooms) " +
           "AND (:minPrice IS NULL OR p.price >= :minPrice) " +
           "AND (:maxPrice IS NULL OR p.price <= :maxPrice) " +
           "ORDER BY p.isFeatured DESC, p.createdAt DESC")
    List<Property> findPropertiesByCriteria(
            @Param("district") String district,
            @Param("propertyType") Property.PropertyType propertyType,
            @Param("bedrooms") Integer bedrooms,
            @Param("minPrice") BigDecimal minPrice,
            @Param("maxPrice") BigDecimal maxPrice);
    
    // Admin service queries
    Long countByStatus(Property.PropertyStatus status);
    Long countByUserId(Long userId);
    Long countByUserIdAndStatus(Long userId, Property.PropertyStatus status);
    Long countByIsFeaturedTrue();
}
