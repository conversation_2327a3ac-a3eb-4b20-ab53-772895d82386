package com.realestate.membership.repository;

import com.realestate.membership.entity.UserMonthlyUsage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserMonthlyUsageRepository extends JpaRepository<UserMonthlyUsage, Long> {
    
    Optional<UserMonthlyUsage> findByUserIdAndYearMonth(Long userId, String yearMonth);
    
    @Query("SELECT umu FROM UserMonthlyUsage umu WHERE umu.user.id = :userId " +
           "AND umu.yearMonth = :yearMonth")
    Optional<UserMonthlyUsage> findCurrentMonthUsage(@Param("userId") Long userId, 
                                                    @Param("yearMonth") String yearMonth);
}
