package com.realestate.membership.service;

import com.realestate.membership.entity.User;
import com.realestate.membership.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.annotation.Order;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
@Slf4j
@Order(1) // Run first
@DependsOn("entityManagerFactory")
public class AdminInitializationService implements CommandLineRunner {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    
    @Value("${app.admin.default-username:admin}")
    private String defaultAdminUsername;
    
    @Value("${app.admin.default-password:admin123}")
    private String defaultAdminPassword;
    
    @Value("${app.admin.default-email:<EMAIL>}")
    private String defaultAdminEmail;

    @Override
    @Transactional
    public void run(String... args) throws Exception {
        // Add delay to ensure database is fully initialized
        try {
            Thread.sleep(3000); // Wait 3 seconds
            initializeDefaultAdmin();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("❌ Interrupted while waiting for database initialization", e);
        }
    }

    private void initializeDefaultAdmin() {
        try {
            // Check if admin user already exists
            if (userRepository.findByRole(User.Role.ADMIN).isEmpty()) {
                log.info("No admin user found. Creating default admin user...");
                
                // Create default admin user
                User admin = new User();
                admin.setUsername(defaultAdminUsername);
                admin.setEmail(defaultAdminEmail);
                admin.setPassword(passwordEncoder.encode(defaultAdminPassword));
                admin.setFirstName("System");
                admin.setLastName("Administrator");
                admin.setRole(User.Role.ADMIN);
                admin.setStatus(User.UserStatus.ACTIVE);
                admin.setEmailVerified(true);
                admin.setOauthProvider(User.OAuthProvider.LOCAL);
                admin.setCreatedAt(LocalDateTime.now());
                admin.setUpdatedAt(LocalDateTime.now());
                admin.setEmailVerifiedAt(LocalDateTime.now());
                
                userRepository.save(admin);
                
                log.info("✅ Default admin user created successfully!");
                log.info("📧 Username: {}", defaultAdminUsername);
                log.info("📧 Email: {}", defaultAdminEmail);
                log.info("🔑 Password: {}", defaultAdminPassword);
                log.warn("⚠️  IMPORTANT: Please change the default admin password after first login!");
                
            } else {
                log.info("✅ Admin user already exists. Skipping initialization.");
            }
            
        } catch (Exception e) {
            log.error("❌ Failed to initialize default admin user", e);
        }
    }
} 