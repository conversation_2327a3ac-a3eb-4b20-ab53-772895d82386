package com.realestate.membership.service;

import com.realestate.membership.dto.AdminStatsResponse;
import com.realestate.membership.dto.PropertyResponse;
import com.realestate.membership.dto.UserManagementResponse;
import com.realestate.membership.entity.*;
import com.realestate.membership.exception.ResourceNotFoundException;
import com.realestate.membership.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class AdminService {

    private final PropertyRepository propertyRepository;
    private final UserRepository userRepository;
    private final UserMembershipRepository userMembershipRepository;
    private final MembershipRepository membershipRepository;
    private final PaymentRepository paymentRepository;
    private final AdminActionRepository adminActionRepository;
    private final ChatSessionRepository chatSessionRepository;
    
    @Value("${app.maintenance.enabled:false}")
    private Boolean maintenanceMode;

    // =====================================================
    // PROPERTY MANAGEMENT
    // =====================================================

    public Page<PropertyResponse> getAllPropertiesWithFilters(
            Pageable pageable, Property.PropertyStatus status, 
            String city, String district, Boolean isFeatured) {
        
        // For now, use basic filtering - can be enhanced with Specification pattern
        if (status != null) {
            return propertyRepository.findByStatusOrderByCreatedAtDesc(status, pageable)
                .map(this::mapToPropertyResponse);
        }
        
        return propertyRepository.findAll(pageable)
            .map(this::mapToPropertyResponse);
    }

    public Page<PropertyResponse> getPendingProperties(Pageable pageable) {
        return propertyRepository.findByStatusOrderByCreatedAtDesc(
            Property.PropertyStatus.PENDING, pageable)
            .map(this::mapToPropertyResponse);
    }

    public void approveProperty(Long propertyId, Boolean isFeatured, String adminNote) {
        Property property = propertyRepository.findById(propertyId)
            .orElseThrow(() -> new ResourceNotFoundException("Property not found with id: " + propertyId));
        
        property.setStatus(Property.PropertyStatus.APPROVED);
        property.setPublishedAt(LocalDateTime.now());
        
        if (isFeatured != null && isFeatured) {
            property.setIsFeatured(true);
        }
        
        propertyRepository.save(property);
        
        // Log admin action
        User admin = getCurrentAdmin(); // You'll need to implement this
        AdminAction action = AdminAction.createPropertyApproval(admin, property, adminNote);
        adminActionRepository.save(action);
        
        log.info("Property {} approved by admin {}", propertyId, admin.getUsername());
    }

    public void rejectProperty(Long propertyId, String reason) {
        Property property = propertyRepository.findById(propertyId)
            .orElseThrow(() -> new ResourceNotFoundException("Property not found with id: " + propertyId));
        
        property.setStatus(Property.PropertyStatus.REJECTED);
        propertyRepository.save(property);
        
        // Log admin action
        User admin = getCurrentAdmin();
        AdminAction action = AdminAction.createPropertyRejection(admin, property, reason);
        adminActionRepository.save(action);
        
        log.info("Property {} rejected by admin {} with reason: {}", propertyId, admin.getUsername(), reason);
    }

    public void toggleFeatureProperty(Long propertyId, Boolean isFeatured) {
        Property property = propertyRepository.findById(propertyId)
            .orElseThrow(() -> new ResourceNotFoundException("Property not found with id: " + propertyId));
        
        property.setIsFeatured(isFeatured);
        propertyRepository.save(property);
        
        log.info("Property {} featured status changed to {}", propertyId, isFeatured);
    }

    public void deleteProperty(Long propertyId, String reason) {
        Property property = propertyRepository.findById(propertyId)
            .orElseThrow(() -> new ResourceNotFoundException("Property not found with id: " + propertyId));
        
        // Log admin action before deletion
        User admin = getCurrentAdmin();
        AdminAction action = AdminAction.createPropertyDeletion(admin, property, reason);
        adminActionRepository.save(action);
        
        propertyRepository.delete(property);
        
        log.info("Property {} deleted by admin {} with reason: {}", propertyId, admin.getUsername(), reason);
    }

    // =====================================================
    // USER MANAGEMENT
    // =====================================================

    public Page<UserManagementResponse> getAllUsersWithFilters(
            Pageable pageable, User.UserStatus status, User.Role role, String search) {
        
        // Basic implementation - can be enhanced with Specification pattern
        return userRepository.findAll(pageable)
            .map(this::mapToUserManagementResponse);
    }

    public void banUser(Long userId, String reason, Integer durationDays) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));
        
        user.setStatus(User.UserStatus.BANNED);
        userRepository.save(user);
        
        // Log admin action
        User admin = getCurrentAdmin();
        AdminAction action = AdminAction.createUserBan(admin, user, reason, durationDays);
        adminActionRepository.save(action);
        
        log.info("User {} banned by admin {} for reason: {}", userId, admin.getUsername(), reason);
    }

    public void unbanUser(Long userId, String reason) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));
        
        user.setStatus(User.UserStatus.ACTIVE);
        userRepository.save(user);
        
        // Log admin action
        User admin = getCurrentAdmin();
        AdminAction action = AdminAction.createUserUnban(admin, user, reason);
        adminActionRepository.save(action);
        
        log.info("User {} unbanned by admin {} for reason: {}", userId, admin.getUsername(), reason);
    }

    public void changeUserRole(Long userId, User.Role newRole, String reason) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));
        
        User.Role oldRole = user.getRole();
        user.setRole(newRole);
        userRepository.save(user);
        
        // Log admin action
        User admin = getCurrentAdmin();
        AdminAction action = AdminAction.createRoleChange(admin, user, oldRole, newRole, reason);
        adminActionRepository.save(action);
        
        log.info("User {} role changed from {} to {} by admin {}", userId, oldRole, newRole, admin.getUsername());
    }

    public UserManagementResponse getUserDetails(Long userId) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));
        
        return mapToUserManagementResponse(user);
    }

    // =====================================================
    // ANALYTICS & STATISTICS
    // =====================================================

    public AdminStatsResponse getDashboardStats(Integer days) {
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        
        return AdminStatsResponse.builder()
            .overview(buildOverviewStats(startDate))
            .revenue(buildRevenueStats(startDate))
            .users(buildUserStats(startDate))
            .properties(buildPropertyStats(startDate))
            .memberships(buildMembershipStats(startDate))
            .systemHealth(buildSystemHealth())
            .recentActivities(buildRecentActivities())
            .chartData(buildChartData(startDate))
            .build();
    }

    public Map<String, Object> getRevenueStats(LocalDateTime startDate, LocalDateTime endDate, String period) {
        if (startDate == null) startDate = LocalDateTime.now().minusDays(30);
        if (endDate == null) endDate = LocalDateTime.now();
        
        BigDecimal totalRevenue = paymentRepository.getTotalRevenueByDateRange(startDate, endDate);
        if (totalRevenue == null) totalRevenue = BigDecimal.ZERO;
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalRevenue", totalRevenue);
        stats.put("period", period);
        stats.put("startDate", startDate);
        stats.put("endDate", endDate);
        stats.put("averageDailyRevenue", totalRevenue.divide(BigDecimal.valueOf(days(startDate, endDate)), RoundingMode.HALF_UP));
        
        return stats;
    }

    public Map<String, Object> getMembershipStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // Total active memberships
        Long totalActive = userMembershipRepository.count();
        stats.put("totalActiveMemberships", totalActive);
        
        // Memberships by type
        List<Membership> allMemberships = membershipRepository.findAll();
        Map<String, Long> membershipsByType = new HashMap<>();
        for (Membership membership : allMemberships) {
            Long count = userMembershipRepository.countActiveSubscriptionsByMembershipId(membership.getId());
            membershipsByType.put(membership.getType().toString(), count);
        }
        stats.put("membershipsByType", membershipsByType);
        
        return stats;
    }

    public Map<String, Object> getPropertyStats(Integer days) {
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalProperties", propertyRepository.count());
        stats.put("pendingProperties", propertyRepository.countByStatus(Property.PropertyStatus.PENDING));
        stats.put("approvedProperties", propertyRepository.countByStatus(Property.PropertyStatus.APPROVED));
        stats.put("rejectedProperties", propertyRepository.countByStatus(Property.PropertyStatus.REJECTED));
        
        return stats;
    }

    public Map<String, Object> getUserStats(Integer days) {
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalUsers", userRepository.count());
        stats.put("activeUsers", userRepository.countByStatus(User.UserStatus.ACTIVE));
        stats.put("bannedUsers", userRepository.countByStatus(User.UserStatus.BANNED));
        
        return stats;
    }

    // =====================================================
    // SYSTEM MANAGEMENT
    // =====================================================

    public Map<String, Object> getSystemHealth() {
        Map<String, Object> health = new HashMap<>();
        
        // Memory usage
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
        long maxMemory = memoryBean.getHeapMemoryUsage().getMax();
        double memoryUsage = (double) usedMemory / maxMemory * 100;
        
        // System load
        OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
        double systemLoad = osBean.getSystemLoadAverage();
        
        health.put("memoryUsage", Math.round(memoryUsage * 100.0) / 100.0);
        health.put("systemLoad", systemLoad);
        health.put("uptime", ManagementFactory.getRuntimeMXBean().getUptime());
        health.put("maintenanceMode", maintenanceMode);
        health.put("status", determineHealthStatus(memoryUsage, systemLoad));
        health.put("lastCheck", LocalDateTime.now());
        
        return health;
    }

    public void toggleMaintenanceMode(Boolean enabled, String message) {
        this.maintenanceMode = enabled;
        log.info("Maintenance mode {} by admin. Message: {}", 
                enabled ? "enabled" : "disabled", message);
    }

    public Map<String, Object> getRecentLogs(int limit, String level) {
        // This is a placeholder - in a real system, you'd integrate with your logging framework
        Map<String, Object> logs = new HashMap<>();
        logs.put("logs", Arrays.asList("System started", "User logged in", "Property approved"));
        logs.put("level", level);
        logs.put("limit", limit);
        logs.put("timestamp", LocalDateTime.now());
        
        return logs;
    }

    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================

    private AdminStatsResponse.OverviewStats buildOverviewStats(LocalDateTime startDate) {
        return AdminStatsResponse.OverviewStats.builder()
            .totalUsers(userRepository.count())
            .totalProperties(propertyRepository.count())
            .totalActiveMemberships(userMembershipRepository.count())
            .totalRevenue(paymentRepository.getTotalRevenueByDateRange(LocalDateTime.now().minusYears(10), LocalDateTime.now()))
            .pendingApprovals(propertyRepository.countByStatus(Property.PropertyStatus.PENDING))
            .todayRegistrations(userRepository.countByCreatedAtAfter(LocalDateTime.now().minusDays(1)))
            .systemUptime((double) ManagementFactory.getRuntimeMXBean().getUptime() / 1000 / 60 / 60) // hours
            .lastUpdateTime(LocalDateTime.now().toString())
            .build();
    }

    private AdminStatsResponse.RevenueStats buildRevenueStats(LocalDateTime startDate) {
        BigDecimal totalRevenue = paymentRepository.getTotalRevenueByDateRange(
            LocalDateTime.now().minusYears(10), LocalDateTime.now());
        if (totalRevenue == null) totalRevenue = BigDecimal.ZERO;
        
        return AdminStatsResponse.RevenueStats.builder()
            .totalRevenue(totalRevenue)
            .monthlyRevenue(paymentRepository.getTotalRevenueByDateRange(
                LocalDateTime.now().minusMonths(1), LocalDateTime.now()))
            .dailyRevenue(paymentRepository.getTotalRevenueByDateRange(
                LocalDateTime.now().minusDays(1), LocalDateTime.now()))
            .totalTransactions(paymentRepository.count())
            .successfulTransactions(paymentRepository.countByStatus(Payment.PaymentStatus.COMPLETED))
            .build();
    }

    private AdminStatsResponse.UserStats buildUserStats(LocalDateTime startDate) {
        return AdminStatsResponse.UserStats.builder()
            .totalUsers(userRepository.count())
            .activeUsers(userRepository.countByStatus(User.UserStatus.ACTIVE))
            .bannedUsers(userRepository.countByStatus(User.UserStatus.BANNED))
            .newUsersToday(userRepository.countByCreatedAtAfter(LocalDateTime.now().minusDays(1)))
            .usersWithActiveSubscriptions(userMembershipRepository.count())
            .build();
    }

    private AdminStatsResponse.PropertyStats buildPropertyStats(LocalDateTime startDate) {
        return AdminStatsResponse.PropertyStats.builder()
            .totalProperties(propertyRepository.count())
            .approvedProperties(propertyRepository.countByStatus(Property.PropertyStatus.APPROVED))
            .pendingProperties(propertyRepository.countByStatus(Property.PropertyStatus.PENDING))
            .rejectedProperties(propertyRepository.countByStatus(Property.PropertyStatus.REJECTED))
            .featuredProperties(propertyRepository.countByIsFeaturedTrue())
            .build();
    }

    private AdminStatsResponse.MembershipStats buildMembershipStats(LocalDateTime startDate) {
        return AdminStatsResponse.MembershipStats.builder()
            .totalActiveMemberships(userMembershipRepository.count())
            .build();
    }

    private AdminStatsResponse.SystemHealth buildSystemHealth() {
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
        long maxMemory = memoryBean.getHeapMemoryUsage().getMax();
        double memoryUsage = (double) usedMemory / maxMemory * 100;
        
        return AdminStatsResponse.SystemHealth.builder()
            .status(determineHealthStatus(memoryUsage, 0.0))
            .memoryUsage(Math.round(memoryUsage * 100.0) / 100.0)
            .maintenanceMode(maintenanceMode)
            .lastHealthCheck(LocalDateTime.now())
            .alerts(new ArrayList<>())
            .build();
    }

    private List<AdminStatsResponse.RecentActivity> buildRecentActivities() {
        List<AdminAction> recentActions = adminActionRepository.findRecentActions(
            PageRequest.of(0, 10));
        
        return recentActions.stream()
            .map(action -> AdminStatsResponse.RecentActivity.builder()
                .type(action.getActionType())
                .description(action.getDescription())
                .userName(action.getAdmin().getUsername())
                .userId(action.getAdmin().getId())
                .timestamp(action.getCreatedAt())
                .severity(action.getSeverity().toString())
                .build())
            .collect(Collectors.toList());
    }

    private Map<String, Object> buildChartData(LocalDateTime startDate) {
        Map<String, Object> chartData = new HashMap<>();
        
        // User growth chart
        List<Object[]> userGrowth = userRepository.getUserGrowthChart(startDate);
        chartData.put("userGrowth", userGrowth);
        
        // Revenue chart
        List<Object[]> revenueChart = paymentRepository.getRevenueChart(startDate);
        chartData.put("revenue", revenueChart);
        
        return chartData;
    }

    private PropertyResponse mapToPropertyResponse(Property property) {
        // Implement mapping logic - can use MapStruct for complex mappings
        PropertyResponse response = new PropertyResponse();
        response.setId(property.getId());
        response.setTitle(property.getTitle());
        response.setDescription(property.getDescription());
        response.setPrice(property.getPrice());
        response.setAddress(property.getAddress());
        response.setCity(property.getCity());
        response.setDistrict(property.getDistrict());
        response.setWard(property.getWard());
        response.setPropertyType(property.getPropertyType());
        response.setListingType(property.getListingType());
        response.setStatus(property.getStatus());
        response.setIsFeatured(property.getIsFeatured());
        response.setCreatedAt(property.getCreatedAt());
        response.setUpdatedAt(property.getUpdatedAt());
        
        return response;
    }

    private UserManagementResponse mapToUserManagementResponse(User user) {
        // Get user statistics
        UserManagementResponse.UserPropertyStats propertyStats = getUserPropertyStats(user);
        UserManagementResponse.UserMembershipInfo membershipInfo = getUserMembershipInfo(user);
        UserManagementResponse.UserActivityStats activityStats = getUserActivityStats(user);
        List<UserManagementResponse.AdminAction> adminActions = getUserAdminActions(user);
        
        return UserManagementResponse.builder()
            .id(user.getId())
            .username(user.getUsername())
            .email(user.getEmail())
            .firstName(user.getFirstName())
            .lastName(user.getLastName())
            .phoneNumber(user.getPhoneNumber())
            .avatarUrl(user.getAvatarUrl())
            .role(user.getRole())
            .status(user.getStatus())
            .emailVerified(user.getEmailVerified())
            .oauthProvider(user.getOauthProvider())
            .googleId(user.getGoogleId())
            .createdAt(user.getCreatedAt())
            .updatedAt(user.getUpdatedAt())
            .lastLoginAt(user.getLastLoginAt())
            .emailVerifiedAt(user.getEmailVerifiedAt())
            .propertyStats(propertyStats)
            .membershipInfo(membershipInfo)
            .activityStats(activityStats)
            .adminActions(adminActions)
            .build();
    }

    private UserManagementResponse.UserPropertyStats getUserPropertyStats(User user) {
        Long totalProperties = propertyRepository.countByUserId(user.getId());
        Long approvedProperties = propertyRepository.countByUserIdAndStatus(user.getId(), Property.PropertyStatus.APPROVED);
        Long pendingProperties = propertyRepository.countByUserIdAndStatus(user.getId(), Property.PropertyStatus.PENDING);
        Long rejectedProperties = propertyRepository.countByUserIdAndStatus(user.getId(), Property.PropertyStatus.REJECTED);
        
        return UserManagementResponse.UserPropertyStats.builder()
            .totalProperties(totalProperties)
            .approvedProperties(approvedProperties)
            .pendingProperties(pendingProperties)
            .rejectedProperties(rejectedProperties)
            .build();
    }

    private UserManagementResponse.UserMembershipInfo getUserMembershipInfo(User user) {
        Optional<UserMembership> activeMembership = userMembershipRepository
            .findActiveByUserId(user.getId(), LocalDateTime.now());
        
        if (activeMembership.isPresent()) {
            UserMembership membership = activeMembership.get();
            return UserManagementResponse.UserMembershipInfo.builder()
                .hasActiveMembership(true)
                .currentMembershipType(membership.getMembership().getType().toString())
                .currentMembershipName(membership.getMembership().getName())
                .membershipStartDate(membership.getStartDate())
                .membershipEndDate(membership.getEndDate())
                .propertiesUsed(membership.getPropertiesUsed())
                .maxProperties(membership.getMembership().getMaxProperties())
                .autoRenewal(membership.getAutoRenewal())
                .membershipStatus(membership.getStatus().toString())
                .build();
        }
        
        return UserManagementResponse.UserMembershipInfo.builder()
            .hasActiveMembership(false)
            .build();
    }

    private UserManagementResponse.UserActivityStats getUserActivityStats(User user) {
        Long totalProperties = propertyRepository.countByUserId(user.getId());
        Long totalChatSessions = chatSessionRepository.countByUserId(user.getId());
        
        return UserManagementResponse.UserActivityStats.builder()
            .totalProperties(totalProperties)
            .totalChatSessions(totalChatSessions)
            .lastActivity(user.getLastLoginAt())
            .build();
    }

    private List<UserManagementResponse.AdminAction> getUserAdminActions(User user) {
        List<AdminAction> actions = adminActionRepository.findByTargetUserOrderByCreatedAtDesc(
            user, PageRequest.of(0, 10)).getContent();
        
        return actions.stream()
            .map(action -> UserManagementResponse.AdminAction.builder()
                .id(action.getId())
                .actionType(action.getActionType())
                .description(action.getDescription())
                .reason(action.getReason())
                .adminUsername(action.getAdmin().getUsername())
                .adminId(action.getAdmin().getId())
                .timestamp(action.getCreatedAt())
                .severity(action.getSeverity().toString())
                .isReversible(action.getIsReversible())
                .expiresAt(action.getExpiresAt())
                .build())
            .collect(Collectors.toList());
    }

    private User getCurrentAdmin() {
        // This should get the current authenticated admin user
        // For now, returning a dummy admin - implement with Spring Security
        return userRepository.findByRole(User.Role.ADMIN).stream().findFirst()
            .orElseThrow(() -> new RuntimeException("No admin user found"));
    }

    private String determineHealthStatus(double memoryUsage, double systemLoad) {
        if (memoryUsage > 90 || systemLoad > 10) {
            return "CRITICAL";
        } else if (memoryUsage > 70 || systemLoad > 5) {
            return "WARNING";
        }
        return "HEALTHY";
    }

    private long days(LocalDateTime start, LocalDateTime end) {
        return java.time.temporal.ChronoUnit.DAYS.between(start, end);
    }
} 