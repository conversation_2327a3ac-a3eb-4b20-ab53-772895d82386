package com.realestate.membership.service;

import com.realestate.membership.entity.*;
import com.realestate.membership.repository.*;
import com.realestate.membership.service.tools.PropertySearchTool;
import com.theokanning.openai.completion.chat.ChatMessage;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ChatbotService {

    private final OpenAIService openAIService;
    private final ChatSessionRepository chatSessionRepository;
    private final ChatMessageRepository chatMessageRepository;
    private final AIAgentRepository aiAgentRepository;
    private final AgentExecutionRepository agentExecutionRepository;
    private final PropertyService propertyService;
    private final UserRepository userRepository;
    private final PropertySearchTool propertySearchTool;
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Value("${chatbot.max-history:10}")
    private Integer maxHistory;
    
    @Value("${chatbot.concurrent-agents:3}")
    private Integer concurrentAgents;
    
    @Value("${chatbot.confidence-threshold:0.5}")
    private Double confidenceThreshold;

    /**
     * Enhanced method that returns both text and structured property data
     */
    @Transactional
    public ChatResponse processMessageEnhanced(Long userId, String userMessage, Long sessionId) {
        try {
            log.info("Processing enhanced message from user {} in session {}", userId, sessionId);
            
            // Get or create chat session
            ChatSession session = getOrCreateSession(userId, sessionId);
            
            // Save user message
            com.realestate.membership.entity.ChatMessage userMsg = saveMessage(
                session, userMessage, com.realestate.membership.entity.ChatMessage.MessageType.USER);
            
            // Analyze intent and route to appropriate agents
            String intent = openAIService.analyzeIntent(userMessage);
            log.debug("Detected intent: {}", intent);
            
            // Get relevant agents based on intent
            List<AIAgent> agents = getAgentsForIntent(intent);
            
            // Execute agents concurrently with enhanced response
            Map<AIAgent, AgentResult> agentResults = executeAgentsConcurrentlyEnhanced(agents, userMessage, session);
            
            // Select best response and merge property results
            ChatResponse finalResponse = selectBestResponseEnhanced(agentResults, userMsg);
            
            // Save bot response
            saveMessage(session, finalResponse.getTextResponse(), com.realestate.membership.entity.ChatMessage.MessageType.BOT);
            
            // Update session activity
            updateSessionActivity(session);
            
            return finalResponse;
            
        } catch (Exception e) {
            log.error("Error processing enhanced message", e);
            return new ChatResponse(
                "Xin lỗi, tôi gặp lỗi khi xử lý yêu cầu của bạn. Vui lòng thử lại.",
                List.of()
            );
        }
    }

    /**
     * Legacy method for backward compatibility
     */
    @Transactional
    public String processMessage(Long userId, String userMessage, Long sessionId) {
        return processMessageEnhanced(userId, userMessage, sessionId).getTextResponse();
    }

    /**
     * Process user message and generate AI response - Legacy
     */
    @Transactional
    public String processMessageLegacy(Long userId, String userMessage, Long sessionId) {
        try {
            log.info("Processing message from user {} in session {}", userId, sessionId);
            
            // Get or create chat session
            ChatSession session = getOrCreateSession(userId, sessionId);
            
            // Save user message
            com.realestate.membership.entity.ChatMessage userMsg = saveMessage(
                session, userMessage, com.realestate.membership.entity.ChatMessage.MessageType.USER);
            
            // Analyze intent and route to appropriate agents
            String intent = openAIService.analyzeIntent(userMessage);
            log.debug("Detected intent: {}", intent);
            
            // Get relevant agents based on intent
            List<AIAgent> agents = getAgentsForIntent(intent);
            
            // Execute agents concurrently (function calling will handle property search)
            Map<AIAgent, String> agentResponses = executeAgentsConcurrently(agents, userMessage, session);
            
            // Select best response
            String bestResponse = selectBestResponse(agentResponses, userMsg);
            
            // Save bot response
            saveMessage(session, bestResponse, com.realestate.membership.entity.ChatMessage.MessageType.BOT);
            
            // Update session activity
            updateSessionActivity(session);
            
            return bestResponse;
            
        } catch (Exception e) {
            log.error("Error processing legacy message", e);
            return "Xin lỗi, tôi gặp lỗi khi xử lý yêu cầu của bạn. Vui lòng thử lại.";
        }
    }
    
    private Map<AIAgent, AgentResult> executeAgentsConcurrentlyEnhanced(
            List<AIAgent> agents, String userMessage, ChatSession session) {
        
        List<CompletableFuture<Map.Entry<AIAgent, AgentResult>>> futures = agents.stream()
            .limit(concurrentAgents)
            .map(agent -> CompletableFuture.supplyAsync(() -> {
                long startTime = System.currentTimeMillis();
                
                try {
                    // Build context-aware prompt
                    String contextualPrompt = buildContextualPrompt(agent, userMessage, session);
                    
                    // Check if this is a property search query
                    boolean isPropertySearch = userMessage.toLowerCase().contains("tìm") || 
                                             userMessage.toLowerCase().contains("search") ||
                                             userMessage.toLowerCase().contains("căn hộ") ||
                                             userMessage.toLowerCase().contains("nhà") ||
                                             userMessage.toLowerCase().contains("giá");
                    
                    String response;
                    List<PropertySearchTool.PropertyResultItem> propertyResults = List.of();
                    
                    if (isPropertySearch) {
                        // For property searches, manually extract criteria and call search tool
                        log.info("Detected property search, calling PropertySearchTool directly");
                        
                        // Extract search criteria from user message
                        String searchArgs = extractSearchArguments(userMessage);
                        
                        // Call PropertySearchTool directly
                        PropertySearchTool.PropertySearchResult searchResult = 
                            propertySearchTool.executeSearchEnhanced(searchArgs);
                        
                        response = searchResult.getTextResponse();
                        propertyResults = searchResult.getProperties();
                        
                        log.info("PropertySearchTool returned {} properties", propertyResults.size());
                    } else {
                        // For non-property queries, use OpenAI normally
                        response = openAIService.generateResponse(contextualPrompt, agent.getSystemPrompt());
                    }
                    
                    AgentResult result = new AgentResult(response, propertyResults);
                    
                    // Save execution record
                    saveAgentExecution(agent, userMessage, response, 
                        (int)(System.currentTimeMillis() - startTime), AgentExecution.ExecutionStatus.SUCCESS);
                    
                    return Map.entry(agent, result);
                    
                } catch (Exception e) {
                    log.error("Error executing agent {}", agent.getName(), e);
                    saveAgentExecution(agent, userMessage, null, 
                        (int)(System.currentTimeMillis() - startTime), AgentExecution.ExecutionStatus.FAILED);
                    
                    AgentResult result = new AgentResult(
                        "Agent tạm thời không khả dụng.",
                        List.of()
                    );
                    return Map.entry(agent, result);
                }
            }))
            .collect(Collectors.toList());
        
        return futures.stream()
            .map(CompletableFuture::join)
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue
            ));
    }
    
    /**
     * Extract search arguments from user message
     */
    private String extractSearchArguments(String userMessage) {
        Map<String, Object> args = new HashMap<>();
        
        // Extract price from message
        if (userMessage.contains("dưới") && userMessage.contains("tỷ")) {
            String[] parts = userMessage.split("dưới");
            if (parts.length > 1) {
                String pricePart = parts[1].trim();
                if (pricePart.contains("10 tỷ")) {
                    args.put("maxPrice", 10000000000L);
                }
                if (pricePart.contains("5 tỷ")) {
                    args.put("maxPrice", 5000000000L);
                }
                if (pricePart.contains("15 tỷ")) {
                    args.put("maxPrice", 15000000000L);
                }
            }
        }
        
        // Extract property type
        if (userMessage.contains("căn hộ")) {
            args.put("propertyType", "APARTMENT");
        } else if (userMessage.contains("nhà")) {
            args.put("propertyType", "HOUSE");
        } else if (userMessage.contains("biệt thự")) {
            args.put("propertyType", "VILLA");
        }
        
        // Extract district
        if (userMessage.contains("Quận 1")) {
            args.put("district", "Quận 1");
        } else if (userMessage.contains("Quận 7")) {
            args.put("district", "Quận 7");
        } else if (userMessage.contains("Thủ Đức")) {
            args.put("district", "Thủ Đức");
        }
        
        try {
            return objectMapper.writeValueAsString(args);
        } catch (Exception e) {
            log.warn("Error creating search arguments", e);
            return "{}";
        }
    }
    
    private ChatResponse selectBestResponseEnhanced(Map<AIAgent, AgentResult> agentResults, 
            com.realestate.membership.entity.ChatMessage userMessage) {
        
        if (agentResults.isEmpty()) {
            return new ChatResponse(
                "Xin lỗi, tôi không thể xử lý yêu cầu của bạn lúc này.",
                List.of()
            );
        }
        
        // Select the agent with highest priority (lowest priority number)
        Map.Entry<AIAgent, AgentResult> bestEntry = agentResults.entrySet().stream()
            .min(Comparator.comparing(entry -> entry.getKey().getPriority()))
            .orElse(agentResults.entrySet().iterator().next());
        
        AgentResult bestResult = bestEntry.getValue();
        
        // Collect all property results from all agents
        List<PropertySearchTool.PropertyResultItem> allProperties = agentResults.values().stream()
            .flatMap(result -> result.getPropertyResults().stream())
            .distinct() // Remove duplicates based on property ID
            .limit(20) // Limit total results
            .collect(Collectors.toList());
        
        return new ChatResponse(bestResult.getTextResponse(), allProperties);
    }

    /**
     * Get conversation history
     */
    public List<Map<String, Object>> getConversationHistory(Long sessionId, int limit) {
        List<com.realestate.membership.entity.ChatMessage> messages = 
            chatMessageRepository.findBySessionIdOrderByCreatedAtDesc(sessionId)
                .stream()
                .limit(limit)
                .collect(Collectors.toList());
        
        Collections.reverse(messages);
        
        return messages.stream()
            .map(msg -> {
                Map<String, Object> msgMap = new HashMap<>();
                msgMap.put("id", msg.getId());
                msgMap.put("type", msg.getMessageType().toString().toLowerCase());
                msgMap.put("content", msg.getContent());
                msgMap.put("timestamp", msg.getCreatedAt());
                return msgMap;
            })
            .collect(Collectors.toList());
    }

    /**
     * Create new chat session
     */
    @Transactional
    public ChatSession createNewSession(Long userId, String title) {
        // Load the actual user from database to ensure it's managed by Hibernate
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));
        
        ChatSession session = new ChatSession();
        session.setUser(user);
        session.setTitle(title != null ? title : "New Conversation");
        session.setStatus(ChatSession.SessionStatus.ACTIVE);
        session.setContext("{}"); // Initialize as empty JSON object
        
        return chatSessionRepository.save(session);
    }

    /**
     * Get user's chat sessions
     */
    public List<ChatSession> getUserSessions(Long userId) {
        return chatSessionRepository.findByUserIdAndStatusOrderByLastActivityAtDesc(
            userId, ChatSession.SessionStatus.ACTIVE);
    }

    private ChatSession getOrCreateSession(Long userId, Long sessionId) {
        if (sessionId != null) {
            return chatSessionRepository.findById(sessionId)
                .orElseThrow(() -> new RuntimeException("Session not found"));
        }
        
        return createNewSession(userId, null);
    }

    private com.realestate.membership.entity.ChatMessage saveMessage(
            ChatSession session, String content, com.realestate.membership.entity.ChatMessage.MessageType type) {
        
        com.realestate.membership.entity.ChatMessage message = new com.realestate.membership.entity.ChatMessage();
        message.setSession(session);
        message.setContent(content);
        message.setMessageType(type);
        message.setMetadata("{}"); // Initialize as empty JSON object
        
        return chatMessageRepository.save(message);
    }

    private List<AIAgent> getAgentsForIntent(String intent) {
        // Map intent to agent priorities
        Map<String, List<String>> intentAgentMap = Map.of(
            "PROPERTY_SEARCH", List.of("property_search", "location_expert"),
            "PRICE_INQUIRY", List.of("price_analyst", "investment_advisor"),
            "LOCATION_INFO", List.of("location_expert", "property_search"),
            "INVESTMENT_ADVICE", List.of("investment_advisor", "price_analyst"),
            "LEGAL_PROCESS", List.of("legal_consultant"),
            "GENERAL", List.of("property_search") // Default agent
        );
        
        List<String> agentNames = intentAgentMap.getOrDefault(intent, 
            List.of("property_search"));
        
        return aiAgentRepository.findByNameInAndIsActiveTrueOrderByPriority(agentNames);
    }

    private Map<AIAgent, String> executeAgentsConcurrently(
            List<AIAgent> agents, String userMessage, ChatSession session) {
        
        List<CompletableFuture<Map.Entry<AIAgent, String>>> futures = agents.stream()
            .limit(concurrentAgents)
            .map(agent -> CompletableFuture.supplyAsync(() -> {
                long startTime = System.currentTimeMillis();
                
                try {
                    // Build context-aware prompt (function calling handles property search)
                    String contextualPrompt = buildContextualPrompt(agent, userMessage, session);
                    
                    // Generate response (OpenAI will use function calling for property search)
                    String response = openAIService.generateResponse(contextualPrompt, agent.getSystemPrompt());
                    
                    // Save execution record
                    saveAgentExecution(agent, userMessage, response, 
                        (int)(System.currentTimeMillis() - startTime), AgentExecution.ExecutionStatus.SUCCESS);
                    
                    return Map.entry(agent, response);
                    
                } catch (Exception e) {
                    log.error("Error executing agent {}", agent.getName(), e);
                    saveAgentExecution(agent, userMessage, null, 
                        (int)(System.currentTimeMillis() - startTime), AgentExecution.ExecutionStatus.FAILED);
                    return Map.entry(agent, "Agent tạm thời không khả dụng.");
                }
            }))
            .collect(Collectors.toList());
        
        return futures.stream()
            .map(CompletableFuture::join)
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue
            ));
    }

    private String buildContextualPrompt(AIAgent agent, String userMessage, ChatSession session) {
        StringBuilder prompt = new StringBuilder();
        
        // Add recent conversation history
        List<com.realestate.membership.entity.ChatMessage> recentMessages = 
            chatMessageRepository.findBySessionIdOrderByCreatedAtDesc(session.getId())
                .stream()
                .limit(maxHistory)
                .collect(Collectors.toList());
        
        if (!recentMessages.isEmpty()) {
            prompt.append("Lịch sử hội thoại gần đây:\n");
            Collections.reverse(recentMessages);
            recentMessages.forEach(msg -> 
                prompt.append(String.format("%s: %s\n", 
                    msg.getMessageType() == com.realestate.membership.entity.ChatMessage.MessageType.USER ? "Khách hàng" : "Chuyên gia", 
                    msg.getContent())));
            prompt.append("\n");
        }
        
        // Add agent-specific prompt template
        String agentPrompt = agent.getPromptTemplate().replace("{user_message}", userMessage);
        prompt.append(agentPrompt);
        
        return prompt.toString();
    }

    private String selectBestResponse(Map<AIAgent, String> agentResponses, 
            com.realestate.membership.entity.ChatMessage userMessage) {
        
        if (agentResponses.isEmpty()) {
            return "Xin lỗi, tôi không thể xử lý yêu cầu của bạn lúc này.";
        }
        
        // For now, select based on agent priority
        // In future, implement ML-based response ranking
        return agentResponses.entrySet().stream()
            .min(Comparator.comparing(entry -> entry.getKey().getPriority()))
            .map(Map.Entry::getValue)
            .orElse("Xin lỗi, tôi không thể tạo ra phản hồi phù hợp.");
    }

    private void saveAgentExecution(AIAgent agent, String input, String response, 
            int executionTime, AgentExecution.ExecutionStatus status) {
        try {
            AgentExecution execution = new AgentExecution();
            execution.setAgent(agent);
            execution.setInputPrompt(input);
            execution.setRawResponse(response);
            execution.setExecutionTime(executionTime);
            execution.setStatus(status);
            
            agentExecutionRepository.save(execution);
        } catch (Exception e) {
            log.error("Error saving agent execution", e);
        }
    }

    private void updateSessionActivity(ChatSession session) {
        session.setLastActivityAt(LocalDateTime.now());
        session.setMessageCount(session.getMessageCount() + 1);
        chatSessionRepository.save(session);
    }
    
    // Inner classes for enhanced responses
    public static class ChatResponse {
        private final String textResponse;
        private final List<PropertySearchTool.PropertyResultItem> properties;
        
        public ChatResponse(String textResponse, List<PropertySearchTool.PropertyResultItem> properties) {
            this.textResponse = textResponse;
            this.properties = properties != null ? properties : List.of();
        }
        
        public String getTextResponse() { return textResponse; }
        public List<PropertySearchTool.PropertyResultItem> getProperties() { return properties; }
        public boolean hasProperties() { return !properties.isEmpty(); }
    }
    
    public static class AgentResult {
        private final String textResponse;
        private final List<PropertySearchTool.PropertyResultItem> propertyResults;
        
        public AgentResult(String textResponse, List<PropertySearchTool.PropertyResultItem> propertyResults) {
            this.textResponse = textResponse;
            this.propertyResults = propertyResults != null ? propertyResults : List.of();
        }
        
        public String getTextResponse() { return textResponse; }
        public List<PropertySearchTool.PropertyResultItem> getPropertyResults() { return propertyResults; }
    }
}
