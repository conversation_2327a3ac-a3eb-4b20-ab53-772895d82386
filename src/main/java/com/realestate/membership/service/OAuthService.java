package com.realestate.membership.service;

import com.realestate.membership.entity.User;
import com.realestate.membership.repository.UserRepository;
import com.realestate.membership.security.JwtTokenProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class OAuthService {

    private final UserRepository userRepository;
    private final JwtTokenProvider jwtTokenProvider;
    private final PasswordEncoder passwordEncoder;
    private final EmailService emailService;
    private final NotificationService notificationService;

    /**
     * Process OAuth user and create/update user account
     */
    @Transactional
    public Map<String, Object> processOAuthUser(OAuth2User oauth2User) {
        String email = oauth2User.getAttribute("email");
        String googleId = oauth2User.getAttribute("sub");
        String name = oauth2User.getAttribute("name");
        String picture = oauth2User.getAttribute("picture");
        Boolean emailVerified = oauth2User.getAttribute("email_verified");

        log.debug("Processing OAuth user: email={}, googleId={}", email, googleId);

        // Check if user already exists by Google ID
        Optional<User> existingUserByGoogleId = userRepository.findByGoogleId(googleId);
        if (existingUserByGoogleId.isPresent()) {
            User user = existingUserByGoogleId.get();
            updateUserLoginInfo(user, picture);
            return createAuthResponse(user);
        }

        // Check if user exists by email
        Optional<User> existingUserByEmail = userRepository.findByEmail(email);
        if (existingUserByEmail.isPresent()) {
            User user = existingUserByEmail.get();
            // Link Google account to existing user
            linkGoogleAccount(user, googleId, picture);
            return createAuthResponse(user);
        }

        // Create new user
        User newUser = createNewOAuthUser(email, googleId, name, picture, emailVerified);
        return createAuthResponse(newUser);
    }

    /**
     * Link OAuth account to existing user
     */
    @Transactional
    public Map<String, Object> linkOAuthAccount(OAuth2User oauth2User, String existingEmail, String password) {
        String googleId = oauth2User.getAttribute("sub");
        String picture = oauth2User.getAttribute("picture");

        // Verify existing user credentials
        Optional<User> existingUser = userRepository.findByEmail(existingEmail);
        if (existingUser.isEmpty()) {
            throw new RuntimeException("User not found with email: " + existingEmail);
        }

        User user = existingUser.get();
        
        // Verify password for local accounts
        if (user.getOauthProvider() == User.OAuthProvider.LOCAL) {
            if (!passwordEncoder.matches(password, user.getPassword())) {
                throw new RuntimeException("Invalid password");
            }
        }

        // Link Google account
        linkGoogleAccount(user, googleId, picture);
        
        return createAuthResponse(user);
    }

    /**
     * Disconnect OAuth account
     */
    @Transactional
    public void disconnectOAuthAccount(Long userId) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("User not found"));

        if (user.getOauthProvider() == User.OAuthProvider.LOCAL) {
            throw new RuntimeException("User is not using OAuth authentication");
        }

        // Reset OAuth fields
        user.setGoogleId(null);
        user.setAvatarUrl(null);
        user.setOauthProvider(User.OAuthProvider.LOCAL);
        
        userRepository.save(user);
        
        log.info("OAuth account disconnected for user: {}", userId);
    }

    private User createNewOAuthUser(String email, String googleId, String name, String picture, Boolean emailVerified) {
        User newUser = new User();
        newUser.setEmail(email);
        newUser.setGoogleId(googleId);
        newUser.setUsername(generateUsernameFromEmail(email));
        newUser.setPassword(passwordEncoder.encode("oauth_user_" + System.currentTimeMillis())); // Dummy password
        newUser.setOauthProvider(User.OAuthProvider.GOOGLE);
        newUser.setAvatarUrl(picture);
        newUser.setEmailVerified(emailVerified != null ? emailVerified : false);
        newUser.setEmailVerifiedAt(emailVerified == Boolean.TRUE ? LocalDateTime.now() : null);
        newUser.setLastLoginAt(LocalDateTime.now());
        newUser.setRole(User.Role.USER);
        newUser.setStatus(User.UserStatus.ACTIVE);

        // Parse name
        if (name != null && !name.trim().isEmpty()) {
            String[] nameParts = name.trim().split("\\s+", 2);
            newUser.setFirstName(nameParts[0]);
            if (nameParts.length > 1) {
                newUser.setLastName(nameParts[1]);
            }
        } else {
            newUser.setFirstName("User");
        }

        User savedUser = userRepository.save(newUser);
        log.info("Created new OAuth user: {}", savedUser.getEmail());
        
        // Send welcome email and notification for new OAuth users
        if (savedUser.getEmailVerified()) {
            try {
                emailService.sendWelcomeEmail(savedUser);
                notificationService.notifyWelcome(savedUser);
            } catch (Exception e) {
                log.error("Failed to send welcome email/notification for OAuth user: {}", savedUser.getEmail(), e);
            }
        }
        
        return savedUser;
    }

    private void linkGoogleAccount(User user, String googleId, String picture) {
        user.setGoogleId(googleId);
        user.setOauthProvider(User.OAuthProvider.GOOGLE);
        if (picture != null) {
            user.setAvatarUrl(picture);
        }
        user.setEmailVerified(true);
        user.setEmailVerifiedAt(LocalDateTime.now());
        updateUserLoginInfo(user, picture);
        
        log.info("Linked Google account for user: {}", user.getEmail());
    }

    private void updateUserLoginInfo(User user, String picture) {
        user.updateLastLogin();
        if (picture != null && !picture.equals(user.getAvatarUrl())) {
            user.setAvatarUrl(picture);
        }
        userRepository.save(user);
    }

    private Map<String, Object> createAuthResponse(User user) {
        String token = jwtTokenProvider.generateToken(user);
        
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", user.getId());
        userInfo.put("username", user.getUsername());
        userInfo.put("email", user.getEmail());
        userInfo.put("firstName", user.getFirstName());
        userInfo.put("lastName", user.getLastName());
        userInfo.put("avatarUrl", user.getAvatarUrl());
        userInfo.put("role", user.getRole().toString());
        userInfo.put("oauthProvider", user.getOauthProvider().toString());
        userInfo.put("emailVerified", user.getEmailVerified());

        Map<String, Object> response = new HashMap<>();
        response.put("user", userInfo);
        response.put("token", token);
        response.put("tokenType", "Bearer");
        
        return response;
    }

    private String generateUsernameFromEmail(String email) {
        String baseUsername = email.split("@")[0].toLowerCase();
        
        // Check if username already exists
        Optional<User> existingUser = userRepository.findByUsername(baseUsername);
        if (existingUser.isEmpty()) {
            return baseUsername;
        }
        
        // Generate unique username with suffix
        int suffix = 1;
        String uniqueUsername;
        do {
            uniqueUsername = baseUsername + suffix;
            suffix++;
        } while (userRepository.findByUsername(uniqueUsername).isPresent());
        
        return uniqueUsername;
    }
}