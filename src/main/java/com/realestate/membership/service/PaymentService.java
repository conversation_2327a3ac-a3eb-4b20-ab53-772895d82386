package com.realestate.membership.service;

import com.realestate.membership.dto.PaymentRequest;
import com.realestate.membership.dto.PaymentResponse;
import com.realestate.membership.entity.Membership;
import com.realestate.membership.entity.Payment;
import com.realestate.membership.entity.User;
import com.realestate.membership.entity.UserMembership;
import com.realestate.membership.exception.ResourceNotFoundException;
import com.realestate.membership.repository.MembershipRepository;
import com.realestate.membership.repository.PaymentRepository;
import com.realestate.membership.repository.UserMembershipRepository;
import com.realestate.membership.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import jakarta.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class PaymentService {

    private final PaymentRepository paymentRepository;
    private final UserRepository userRepository;
    private final MembershipRepository membershipRepository;
    private final UserMembershipRepository userMembershipRepository;
    private final NotificationService notificationService;
    private final RestTemplate restTemplate;
    
    // Payment Configuration Constants
    @Value("${payment.currency:VND}")
    private String defaultCurrency;
    
    @Value("${payment.min-amount:1000}")
    private BigDecimal minPaymentAmount;
    
    @Value("${payment.max-amount:100000000}")
    private BigDecimal maxPaymentAmount;
    
    @Value("${payment.fee-rate:0.02}")
    private BigDecimal feeRate;
    
    @Value("${payment.auto-refund.enabled:false}")
    private Boolean autoRefundEnabled;
    
    @Value("${exchange.api.url:https://api.exchangerate-api.com/v4/latest/VND}")
    private String exchangeApiUrl;

    // =====================================================
    // PAYMENT CREATION & PROCESSING
    // =====================================================

    public PaymentResponse createPayment(PaymentRequest request, HttpServletRequest httpRequest) {
        try {
            // Validate request
            validatePaymentRequest(request);
            
            // Get current user
            String username = SecurityContextHolder.getContext().getAuthentication().getName();
            User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("User not found"));
            
            // Get membership
            Membership membership = membershipRepository.findById(request.getMembershipId())
                .orElseThrow(() -> new RuntimeException("Membership not found"));
            
            // Create payment record
            Payment payment = createPaymentRecord(request, user, membership);
            
            // Set client information
            setClientInformation(payment, httpRequest);
            
            payment = paymentRepository.save(payment);
            
            log.info("Payment created successfully: {}", payment.getId());
            
            return mapToPaymentResponse(payment);
            
        } catch (Exception e) {
            log.error("Error creating payment", e);
            throw new RuntimeException("Failed to create payment: " + e.getMessage());
        }
    }

    public void processSuccessfulPayment(Payment payment) {
        try {
            log.info("Processing successful payment: {}", payment.getId());
            
            // Get membership details
            Membership membership = membershipRepository.findById(payment.getMembershipId())
                .orElseThrow(() -> new ResourceNotFoundException("Membership not found"));
            
            // Create or extend user membership
            createOrExtendUserMembership(payment.getUser(), membership, payment);
            
            // Update payment
            payment.setStatus(Payment.PaymentStatus.COMPLETED);
            payment.setCompletedAt(LocalDateTime.now());
            paymentRepository.save(payment);
            
            log.info("User membership updated successfully for payment: {}", payment.getId());
            
            // Send payment success notification
            notificationService.notifyPaymentSuccess(payment.getUser(), payment.getId(), 
                                                   payment.getAmount().toString());
            
        } catch (Exception e) {
            log.error("Error processing successful payment", e);
            // Don't throw exception here to avoid payment gateway callback failures
        }
    }

    public PaymentResponse verifyPaymentStatus(Long paymentId) {
        Payment payment = paymentRepository.findById(paymentId)
            .orElseThrow(() -> new ResourceNotFoundException("Payment not found"));
        
        // Here you could add actual gateway verification calls
        // For now, return current status
        return mapToPaymentResponse(payment);
    }

    public void cancelPayment(Long paymentId, String reason) {
        Payment payment = paymentRepository.findById(paymentId)
            .orElseThrow(() -> new ResourceNotFoundException("Payment not found"));
        
        if (payment.getStatus() != Payment.PaymentStatus.PENDING) {
            throw new IllegalStateException("Can only cancel pending payments");
        }
        
        payment.setStatus(Payment.PaymentStatus.CANCELLED);
        payment.setFailureReason(reason);
        payment.setUpdatedAt(LocalDateTime.now());
        
        paymentRepository.save(payment);
    }

    // =====================================================
    // PAYMENT RETRIEVAL & LISTING
    // =====================================================

    public PaymentResponse getPaymentById(Long paymentId) {
        Payment payment = paymentRepository.findById(paymentId)
            .orElseThrow(() -> new ResourceNotFoundException("Payment not found"));
        
        return mapToPaymentResponse(payment);
    }

    public Page<PaymentResponse> getUserPayments(Pageable pageable, Payment.PaymentStatus status) {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new RuntimeException("User not found"));
        
        Page<Payment> payments;
        if (status != null) {
            payments = paymentRepository.findByUserIdAndStatusOrderByCreatedAtDesc(
                user.getId(), status, pageable);
        } else {
            payments = paymentRepository.findByUserIdOrderByCreatedAtDesc(
                user.getId(), pageable);
        }
        
        return payments.map(this::mapToPaymentResponse);
    }

    public Page<PaymentResponse> getPaymentsByMembership(Long membershipId, Pageable pageable) {
        return paymentRepository.findByMembershipIdOrderByCreatedAtDesc(membershipId, pageable)
            .map(this::mapToPaymentResponse);
    }

    public Page<PaymentResponse> getAllPayments(Pageable pageable, 
                                              Payment.PaymentStatus status, 
                                              Payment.PaymentMethod method) {
        // Admin only - implement filtering logic
        Page<Payment> payments;
        
        if (status != null && method != null) {
            payments = paymentRepository.findByStatusAndPaymentMethodOrderByCreatedAtDesc(
                status, method, pageable);
        } else if (status != null) {
            payments = paymentRepository.findByStatusOrderByCreatedAtDesc(status, pageable);
        } else if (method != null) {
            payments = paymentRepository.findByPaymentMethodOrderByCreatedAtDesc(method, pageable);
        } else {
            payments = paymentRepository.findAll(pageable);
        }
        
        return payments.map(this::mapToPaymentResponse);
    }

    // =====================================================
    // REFUND PROCESSING
    // =====================================================

    public PaymentResponse processRefund(Long paymentId, String reason, String refundAmount) {
        Payment originalPayment = paymentRepository.findById(paymentId)
            .orElseThrow(() -> new ResourceNotFoundException("Payment not found"));
        
        if (originalPayment.getStatus() != Payment.PaymentStatus.COMPLETED) {
            throw new IllegalStateException("Can only refund completed payments");
        }
        
        BigDecimal refundAmountBD = refundAmount != null ? 
            new BigDecimal(refundAmount) : originalPayment.getAmount();
        
        if (refundAmountBD.compareTo(originalPayment.getAmount()) > 0) {
            throw new IllegalArgumentException("Refund amount cannot exceed original payment amount");
        }
        
        // Create refund payment record
        Payment refund = new Payment();
        refund.setUser(originalPayment.getUser());
        refund.setMembershipId(originalPayment.getMembershipId());
        refund.setAmount(refundAmountBD.negate()); // Negative amount for refund
        refund.setPaymentMethod(originalPayment.getPaymentMethod());
        refund.setStatus(Payment.PaymentStatus.COMPLETED);
        refund.setDescription("Refund for payment #" + originalPayment.getId());
        refund.setOrderId("REF" + System.currentTimeMillis());
        refund.setGatewayTransactionId("REFUND_" + originalPayment.getGatewayTransactionId());
        refund.setFailureReason(reason);
        refund.setPaymentDate(LocalDateTime.now());
        refund.setCompletedAt(LocalDateTime.now());
        refund.setCreatedAt(LocalDateTime.now());
        refund.setUpdatedAt(LocalDateTime.now());
        
        refund = paymentRepository.save(refund);
        
        // Update original payment status
        originalPayment.setStatus(Payment.PaymentStatus.REFUNDED);
        originalPayment.setUpdatedAt(LocalDateTime.now());
        paymentRepository.save(originalPayment);
        
        log.info("Refund processed for payment {}: amount={}", paymentId, refundAmountBD);
        
        return mapToPaymentResponse(refund);
    }

    // =====================================================
    // PAYMENT CONFIGURATION & UTILITIES
    // =====================================================

    public Map<String, Object> getAvailablePaymentMethods() {
        Map<String, Object> methods = new HashMap<>();
        
        // VNPay configuration
        Map<String, Object> vnpay = new HashMap<>();
        vnpay.put("name", "VNPay");
        vnpay.put("displayName", "Ví điện tử VNPay");
        vnpay.put("enabled", true);
        vnpay.put("fee", calculateFee(BigDecimal.valueOf(100000), "VNPAY"));
        vnpay.put("minAmount", minPaymentAmount);
        vnpay.put("maxAmount", maxPaymentAmount);
        vnpay.put("currency", defaultCurrency);
        vnpay.put("supportedBanks", List.of("NCB", "AGRIBANK", "SCB", "SACOMBANK", "EXIMBANK", "MSBANK", "NAMABANK", "VNMART", "VIETINBANK", "VIETCOMBANK", "HDBANK", "DONGABANK", "TPBANK", "OJB", "BIDV", "TECHCOMBANK", "VPBANK", "MBBANK", "ACB", "OCB", "IVB", "VISA"));
        
        // MoMo configuration  
        Map<String, Object> momo = new HashMap<>();
        momo.put("name", "MoMo");
        momo.put("displayName", "Ví điện tử MoMo");
        momo.put("enabled", true);
        momo.put("fee", calculateFee(BigDecimal.valueOf(100000), "MOMO"));
        momo.put("minAmount", minPaymentAmount);
        momo.put("maxAmount", maxPaymentAmount);
        momo.put("currency", defaultCurrency);
        momo.put("requestTypes", List.of("captureWallet", "payWithATM"));
        
        // Bank transfer configuration
        Map<String, Object> bankTransfer = new HashMap<>();
        bankTransfer.put("name", "BANK_TRANSFER");
        bankTransfer.put("displayName", "Chuyển khoản ngân hàng");
        bankTransfer.put("enabled", false); // Manual processing required
        bankTransfer.put("fee", BigDecimal.ZERO);
        bankTransfer.put("processingTime", "1-3 business days");
        
        methods.put("vnpay", vnpay);
        methods.put("momo", momo);
        methods.put("bankTransfer", bankTransfer);
        
        return methods;
    }

    public Map<String, Object> getExchangeRates() {
        try {
            // Call external exchange rate API (mock implementation)
            Map<String, Object> rates = new HashMap<>();
            rates.put("VND", 1.0);
            rates.put("USD", 0.000041); // 1 VND = 0.000041 USD (approximate)
            rates.put("EUR", 0.000038); // 1 VND = 0.000038 EUR (approximate)
            rates.put("lastUpdated", LocalDateTime.now().toString());
            
            return rates;
            
        } catch (Exception e) {
            log.warn("Failed to fetch exchange rates, using defaults", e);
            
            Map<String, Object> defaultRates = new HashMap<>();
            defaultRates.put("VND", 1.0);
            defaultRates.put("USD", 0.000041);
            defaultRates.put("EUR", 0.000038);
            defaultRates.put("lastUpdated", "N/A");
            
            return defaultRates;
        }
    }

    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================

    private void validatePaymentRequest(PaymentRequest request) {
        if (request.getAmount().compareTo(minPaymentAmount) < 0) {
            throw new IllegalArgumentException("Payment amount below minimum: " + minPaymentAmount);
        }
        
        if (request.getAmount().compareTo(maxPaymentAmount) > 0) {
            throw new IllegalArgumentException("Payment amount exceeds maximum: " + maxPaymentAmount);
        }
        
        // Check membership exists
        if (!membershipRepository.existsById(request.getMembershipId())) {
            throw new IllegalArgumentException("Invalid membership ID: " + request.getMembershipId());
        }
    }

    private Payment createPaymentRecord(PaymentRequest request, User user, Membership membership) {
        Payment payment = new Payment();
        payment.setUser(user);
        payment.setMembershipId(request.getMembershipId());
        payment.setAmount(request.getAmount());
        payment.setPaymentMethod(request.getPaymentMethod());
        payment.setStatus(Payment.PaymentStatus.PENDING);
        payment.setDescription(request.getDescription());
        payment.setOrderId(generateOrderId(request.getPaymentMethod()));
        payment.setCreatedAt(LocalDateTime.now());
        payment.setUpdatedAt(LocalDateTime.now());
        
        return payment;
    }

    private void setClientInformation(Payment payment, HttpServletRequest request) {
        // Extract client IP
        String clientIp = getClientIP(request);
        String userAgent = request.getHeader("User-Agent");
        
        // Store in gateway response field for now (can be separate fields)
        Map<String, String> clientInfo = new HashMap<>();
        clientInfo.put("ip", clientIp);
        clientInfo.put("userAgent", userAgent);
        
        payment.setGatewayResponse(clientInfo.toString());
    }

    private void createOrExtendUserMembership(User user, Membership membership, Payment payment) {
        Optional<UserMembership> existingMembership = userMembershipRepository
            .findActiveByUserId(user.getId(), LocalDateTime.now());
        
        LocalDateTime startDate = LocalDateTime.now();
        LocalDateTime endDate = startDate.plusMonths(membership.getDurationMonths());
        
        if (existingMembership.isPresent() && existingMembership.get().getMembership().getId().equals(membership.getId())) {
            // Extend existing membership
            UserMembership existing = existingMembership.get();
            if (existing.getEndDate().isAfter(LocalDateTime.now())) {
                // Still active, extend from current end date
                endDate = existing.getEndDate().plusMonths(membership.getDurationMonths());
            }
            existing.setEndDate(endDate);
            existing.setUpdatedAt(LocalDateTime.now());
            userMembershipRepository.save(existing);
        } else {
            // Deactivate existing membership if different
            existingMembership.ifPresent(um -> {
                um.setStatus(UserMembership.MembershipStatus.CANCELLED);
                um.setUpdatedAt(LocalDateTime.now());
                userMembershipRepository.save(um);
            });
            
            // Create new membership
            UserMembership newMembership = new UserMembership();
            newMembership.setUser(user);
            newMembership.setMembership(membership);
            newMembership.setStartDate(startDate);
            newMembership.setEndDate(endDate);
            newMembership.setStatus(UserMembership.MembershipStatus.ACTIVE);
            newMembership.setCreatedAt(LocalDateTime.now());
            newMembership.setUpdatedAt(LocalDateTime.now());
            
            userMembershipRepository.save(newMembership);
        }
    }

    public PaymentResponse mapToPaymentResponse(Payment payment) {
        PaymentResponse response = new PaymentResponse();
        response.setId(payment.getId());
        response.setUserId(payment.getUser().getId());
        response.setUserName(payment.getUser().getUsername());
        response.setMembershipId(payment.getMembershipId());
        response.setAmount(payment.getAmount());
        response.setPaymentMethod(payment.getPaymentMethod());
        response.setStatus(payment.getStatus());
        response.setDescription(payment.getDescription());
        response.setOrderId(payment.getOrderId());
        response.setGatewayTransactionId(payment.getGatewayTransactionId());
        response.setGatewayResponseCode(payment.getGatewayResponseCode());
        response.setGatewayMessage(payment.getGatewayMessage());
        response.setBankCode(payment.getBankCode());
        response.setPaymentDate(payment.getPaymentDate());
        response.setCompletedAt(payment.getCompletedAt());
        response.setFailureReason(payment.getFailureReason());
        response.setCreatedAt(payment.getCreatedAt());
        response.setUpdatedAt(payment.getUpdatedAt());
        
        // Calculate processing fee
        response.setProcessingFee(calculateFee(payment.getAmount(), payment.getPaymentMethod().toString()));
        
        // Set membership details if available
        if (payment.getMembershipId() != null) {
            membershipRepository.findById(payment.getMembershipId())
                .ifPresent(membership -> {
                    response.setMembershipName(membership.getName());
                    response.setMembershipType(membership.getName());
                });
        }
        
        return response;
    }

    private String generateOrderId(Payment.PaymentMethod method) {
        String prefix = switch (method) {
            case VNPAY -> "VNP";
            case MOMO -> "MOMO";
            case BANK_TRANSFER -> "BANK";
            case CASH -> "CASH";
            default -> "PAY";
        };
        
        return prefix + System.currentTimeMillis();
    }

    private BigDecimal calculateFee(BigDecimal amount, String method) {
        if (amount == null) return BigDecimal.ZERO;
        
        BigDecimal rate = switch (method.toUpperCase()) {
            case "VNPAY" -> new BigDecimal("0.015"); // 1.5%
            case "MOMO" -> new BigDecimal("0.02"); // 2%
            case "BANK_TRANSFER" -> new BigDecimal("0.005"); // 0.5%
            default -> feeRate;
        };
        
        return amount.multiply(rate).setScale(0, RoundingMode.UP);
    }

    private String getClientIP(HttpServletRequest request) {
        String xfHeader = request.getHeader("X-Forwarded-For");
        if (xfHeader == null || xfHeader.isEmpty() || "unknown".equalsIgnoreCase(xfHeader)) {
            return request.getRemoteAddr();
        }
        return xfHeader.split(",")[0].trim();
    }
} 