package com.realestate.membership.service;

import com.realestate.membership.dto.PropertyBoostResponse;
import com.realestate.membership.entity.*;
import com.realestate.membership.exception.InsufficientMembershipException;
import com.realestate.membership.exception.ResourceNotFoundException;
import com.realestate.membership.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class PropertyBoostService {
    
    private final PropertyBoostRepository propertyBoostRepository;
    private final UserMonthlyUsageRepository userMonthlyUsageRepository;
    private final PropertyRepository propertyRepository;
    private final UserRepository userRepository;
    private final MembershipService membershipService;
    
    public Map<String, Object> pushPropertyToTop(Long userId, Long propertyId) {
        // Verify property ownership
        Property property = propertyRepository.findById(propertyId)
                .orElseThrow(() -> new ResourceNotFoundException("Property not found"));
        
        if (!property.getUser().getId().equals(userId)) {
            throw new RuntimeException("You don't have permission to boost this property");
        }
        
        // Check if property is approved
        if (property.getStatus() != Property.PropertyStatus.APPROVED) {
            throw new RuntimeException("Only approved properties can be boosted");
        }
        
        // Check user membership and limits
        Optional<UserMembership> activeMembership = membershipService.getUserActiveMembership(userId);
        if (activeMembership.isEmpty()) {
            throw new InsufficientMembershipException("No active membership found");
        }
        
        UserMembership membership = activeMembership.get();
        Integer pushTopLimit = membership.getMembership().getPushTopLimit();
        
        if (pushTopLimit == null || pushTopLimit <= 0) {
            throw new InsufficientMembershipException("Your membership plan doesn't include push top feature");
        }
        
        // Check monthly usage
        String currentMonth = YearMonth.now().toString();
        UserMonthlyUsage monthlyUsage = getOrCreateMonthlyUsage(userId, currentMonth);
        
        if (!monthlyUsage.canUsePushTop(pushTopLimit)) {
            throw new InsufficientMembershipException(
                String.format("Monthly push top limit reached (%d/%d)", 
                            monthlyUsage.getPushTopUsed(), pushTopLimit));
        }
        
        // Check if property is already boosted
        Optional<PropertyBoost> existingBoost = propertyBoostRepository
                .findActiveBoostByPropertyAndType(propertyId, PropertyBoost.BoostType.PUSH_TOP, LocalDateTime.now());
        
        if (existingBoost.isPresent()) {
            throw new RuntimeException("Property is already boosted");
        }
        
        // Create boost
        PropertyBoost boost = new PropertyBoost();
        boost.setProperty(property);
        boost.setUser(property.getUser());
        boost.setBoostType(PropertyBoost.BoostType.PUSH_TOP);
        boost.setBoostStart(LocalDateTime.now());
        boost.setBoostEnd(LocalDateTime.now().plusDays(7)); // 7 days boost
        boost.setIsActive(true);
        
        propertyBoostRepository.save(boost);
        
        // Update monthly usage
        monthlyUsage.incrementPushTopUsage();
        userMonthlyUsageRepository.save(monthlyUsage);
        
        log.info("Property {} pushed to top by user {}", propertyId, userId);
        
        return Map.of(
            "success", true,
            "message", "Property pushed to top successfully",
            "boostId", boost.getId(),
            "boostEnd", boost.getBoostEnd(),
            "remainingUses", pushTopLimit - monthlyUsage.getPushTopUsed()
        );
    }
    
    public Map<String, Object> getUserPushTopStatus(Long userId) {
        Optional<UserMembership> activeMembership = membershipService.getUserActiveMembership(userId);
        if (activeMembership.isEmpty()) {
            return Map.of(
                "hasAccess", false,
                "message", "No active membership found"
            );
        }
        
        UserMembership membership = activeMembership.get();
        Integer pushTopLimit = membership.getMembership().getPushTopLimit();
        
        if (pushTopLimit == null || pushTopLimit <= 0) {
            return Map.of(
                "hasAccess", false,
                "message", "Your membership plan doesn't include push top feature"
            );
        }
        
        String currentMonth = YearMonth.now().toString();
        UserMonthlyUsage monthlyUsage = getOrCreateMonthlyUsage(userId, currentMonth);
        
        return Map.of(
            "hasAccess", true,
            "monthlyLimit", pushTopLimit,
            "used", monthlyUsage.getPushTopUsed(),
            "remaining", pushTopLimit - monthlyUsage.getPushTopUsed(),
            "canUse", monthlyUsage.canUsePushTop(pushTopLimit),
            "resetDate", YearMonth.now().plusMonths(1).atDay(1)
        );
    }
    
    public Page<PropertyBoostResponse> getUserBoostHistory(Long userId, Pageable pageable) {
        return propertyBoostRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable)
                .map(this::mapToResponse);
    }

    public List<PropertyBoostResponse> getUserActiveBoosts(Long userId) {
        return propertyBoostRepository.findActiveBoostsByUserId(userId, LocalDateTime.now())
                .stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }
    
    public List<Long> getCurrentlyBoostedPropertyIds() {
        return propertyBoostRepository.findCurrentlyBoostedPropertyIds(
                PropertyBoost.BoostType.PUSH_TOP, LocalDateTime.now());
    }
    
    private UserMonthlyUsage getOrCreateMonthlyUsage(Long userId, String yearMonth) {
        return userMonthlyUsageRepository.findByUserIdAndYearMonth(userId, yearMonth)
                .orElseGet(() -> {
                    User user = userRepository.findById(userId)
                            .orElseThrow(() -> new ResourceNotFoundException("User not found"));
                    
                    UserMonthlyUsage usage = new UserMonthlyUsage();
                    usage.setUser(user);
                    usage.setYearMonth(yearMonth);
                    usage.setPushTopUsed(0);
                    usage.setAiContentUsed(0);
                    
                    return userMonthlyUsageRepository.save(usage);
                });
    }

    private PropertyBoostResponse mapToResponse(PropertyBoost boost) {
        PropertyBoostResponse response = new PropertyBoostResponse();
        response.setId(boost.getId());
        response.setPropertyId(boost.getProperty().getId());
        response.setPropertyTitle(boost.getProperty().getTitle());
        response.setUserId(boost.getUser().getId());
        response.setBoostType(boost.getBoostType());
        response.setBoostStart(boost.getBoostStart());
        response.setBoostEnd(boost.getBoostEnd());
        response.setIsActive(boost.getIsActive());
        response.setIsCurrentlyActive(boost.isCurrentlyActive());
        response.setCreatedAt(boost.getCreatedAt());
        response.setUpdatedAt(boost.getUpdatedAt());

        // Calculate days remaining
        if (boost.getBoostEnd() != null) {
            long daysRemaining = ChronoUnit.DAYS.between(LocalDateTime.now(), boost.getBoostEnd());
            response.setDaysRemaining(Math.max(0, daysRemaining));
        } else {
            response.setDaysRemaining(0L);
        }

        return response;
    }
}
