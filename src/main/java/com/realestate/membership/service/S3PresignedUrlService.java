package com.realestate.membership.service;

import com.amazonaws.HttpMethod;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.realestate.membership.util.ImageValidationUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URL;
import java.util.Date;

@Service
@RequiredArgsConstructor
public class S3PresignedUrlService {

    private final AmazonS3 amazonS3Client;

    @Value("${aws.s3.bucket-name}")
    private String bucketName;

    @Value("${aws.s3.cdn-domain:}")
    private String cdnDomain;

    /**
     * Upload file and return presigned URL for viewing
     */
    public String uploadFileWithPresignedUrl(MultipartFile file, String folder) throws IOException {
        // Validate file
        ImageValidationUtil.validateImageFile(file);

        // Generate unique filename
        String originalFilename = file.getOriginalFilename();
        String uniqueFilename = ImageValidationUtil.generateUniqueFileName(originalFilename);
        String key = folder + "/" + uniqueFilename;

        // Set object metadata
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentType(file.getContentType());
        metadata.setContentLength(file.getSize());

        // Upload to S3
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, key, file.getInputStream(), metadata);
        PutObjectResult result = amazonS3Client.putObject(putObjectRequest);

        // Generate presigned URL (valid for 24 hours)
        String presignedUrl = generatePresignedUrl(key, 24 * 60); // 24 hours in minutes
        
        return presignedUrl;
    }

    /**
     * Generate presigned URL for existing S3 object
     */
    public String generatePresignedUrl(String key, int expirationMinutes) {
        Date expiration = new Date();
        long expTimeMillis = expiration.getTime();
        expTimeMillis += 1000L * 60 * expirationMinutes; // Convert minutes to milliseconds
        expiration.setTime(expTimeMillis);

        GeneratePresignedUrlRequest generatePresignedUrlRequest = 
                new GeneratePresignedUrlRequest(bucketName, key)
                        .withMethod(HttpMethod.GET)
                        .withExpiration(expiration);

        URL url = amazonS3Client.generatePresignedUrl(generatePresignedUrlRequest);
        return url.toString();
    }

    /**
     * Generate presigned URL from full S3 URL
     */
    public String generatePresignedUrlFromS3Url(String s3Url, int expirationMinutes) {
        String key = extractKeyFromUrl(s3Url);
        return generatePresignedUrl(key, expirationMinutes);
    }

    /**
     * Delete file from S3
     */
    public void deleteFile(String s3Url) {
        String key = extractKeyFromUrl(s3Url);
        amazonS3Client.deleteObject(bucketName, key);
    }

    /**
     * Extract S3 key from full URL
     */
    private String extractKeyFromUrl(String s3Url) {
        if (cdnDomain != null && !cdnDomain.isEmpty() && s3Url.contains(cdnDomain)) {
            return s3Url.substring(s3Url.indexOf(cdnDomain) + cdnDomain.length() + 1);
        }
        
        // For S3 URLs like https://bucket.s3.region.amazonaws.com/key
        int bucketIndex = s3Url.indexOf(bucketName);
        if (bucketIndex != -1) {
            int pathStart = s3Url.indexOf("/", bucketIndex + bucketName.length());
            if (pathStart != -1) {
                return s3Url.substring(pathStart + 1);
            }
        }
        
        // Fallback
        String[] parts = s3Url.split("/");
        if (parts.length >= 4) {
            return String.join("/", java.util.Arrays.copyOfRange(parts, 3, parts.length));
        }
        
        throw new IllegalArgumentException("Cannot extract key from S3 URL: " + s3Url);
    }

    /**
     * Get direct S3 URL (for internal storage)
     */
    public String getDirectS3Url(String key) {
        return amazonS3Client.getUrl(bucketName, key).toString();
    }

    /**
     * Check if object exists in S3
     */
    public boolean objectExists(String key) {
        try {
            amazonS3Client.getObjectMetadata(bucketName, key);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
