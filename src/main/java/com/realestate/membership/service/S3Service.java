package com.realestate.membership.service;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class S3Service {

    private final AmazonS3 amazonS3Client;

    @Value("${aws.s3.bucket-name}")
    private String bucketName;

    @Value("${aws.s3.cdn-domain}")
    private String cdnDomain;

    public String uploadFile(MultipartFile file, String folder) throws IOException {
        String originalFilename = file.getOriginalFilename();
        String fileExtension = getFileExtension(originalFilename);
        String key = folder + "/" + UUID.randomUUID().toString() + "." + fileExtension;

        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentType(file.getContentType());
        metadata.setContentLength(file.getSize());

        PutObjectRequest putObjectRequest = new PutObjectRequest(
                bucketName,
                key,
                file.getInputStream(),
                metadata
        );

        amazonS3Client.putObject(putObjectRequest);

        // Return CDN URL if configured, otherwise return S3 URL
        if (cdnDomain != null && !cdnDomain.isEmpty()) {
            return "https://" + cdnDomain + "/" + key;
        }
        return amazonS3Client.getUrl(bucketName, key).toString();
    }

    public void deleteFile(String fileUrl) {
        String key = getKeyFromUrl(fileUrl);
        amazonS3Client.deleteObject(bucketName, key);
    }

    private String getFileExtension(String filename) {
        if (filename == null) return "";
        int lastDotIndex = filename.lastIndexOf(".");
        return lastDotIndex == -1 ? "" : filename.substring(lastDotIndex + 1).toLowerCase();
    }

    private String getKeyFromUrl(String fileUrl) {
        if (cdnDomain != null && !cdnDomain.isEmpty() && fileUrl.contains(cdnDomain)) {
            return fileUrl.substring(fileUrl.indexOf(cdnDomain) + cdnDomain.length() + 1);
        }
        // For S3 URLs like https://bucket.s3.amazonaws.com/path/file.jpg
        int bucketIndex = fileUrl.indexOf(bucketName);
        if (bucketIndex != -1) {
            int pathStart = fileUrl.indexOf("/", bucketIndex + bucketName.length());
            if (pathStart != -1) {
                return fileUrl.substring(pathStart + 1);
            }
        }
        return fileUrl.substring(fileUrl.indexOf(bucketName) + bucketName.length() + 1);
    }
    
    // Helper methods for validation (used by tests)
    public boolean isValidImageFile(String filename) {
        return com.realestate.membership.util.ImageValidationUtil.isValidImageFile(filename);
    }
    
    public String generateFileName(String originalFilename) {
        return com.realestate.membership.util.ImageValidationUtil.generateUniqueFileName(originalFilename);
    }
} 