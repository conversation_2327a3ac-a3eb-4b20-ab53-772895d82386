package com.realestate.membership.service;

import com.realestate.membership.dto.PaymentRequest;
import com.realestate.membership.dto.PaymentResponse;
import com.realestate.membership.entity.Payment;
import com.realestate.membership.entity.User;
import com.realestate.membership.repository.PaymentRepository;
import com.realestate.membership.repository.UserRepository;
import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.Event;
import com.stripe.model.PaymentIntent;
import com.stripe.model.checkout.Session;
import com.stripe.net.Webhook;
import com.stripe.param.PaymentIntentCreateParams;
import com.stripe.param.checkout.SessionCreateParams;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.PostConstruct;
import jakarta.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class StripeService {

    private final PaymentRepository paymentRepository;
    private final UserRepository userRepository;
    private final PaymentService paymentService;
    
    // Stripe Configuration
    @Value("${stripe.secret-key}")
    private String stripeSecretKey;
    
    @Value("${stripe.publishable-key}")
    private String stripePublishableKey;
    
    @Value("${stripe.webhook-secret}")
    private String webhookSecret;
    
    @Value("${stripe.currency:usd}")
    private String currency;
    
    @Value("${stripe.success-url}")
    private String successUrl;
    
    @Value("${stripe.cancel-url}")
    private String cancelUrl;
    
    @PostConstruct
    private void init() {
        Stripe.apiKey = stripeSecretKey;
        log.info("Stripe service initialized with currency: {}", currency);
    }

    // =====================================================
    // PAYMENT INTENT CREATION (Direct payments)
    // =====================================================

    public Map<String, Object> createPaymentIntent(PaymentRequest request, HttpServletRequest httpRequest) {
        try {
            // Validate request
            if (!request.isValidForStripe()) {
                throw new IllegalArgumentException("Invalid payment method for Stripe");
            }
            
            // Get current user
            String username = SecurityContextHolder.getContext().getAuthentication().getName();
            User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("User not found"));
            
            // Create payment record
            Payment payment = createPaymentRecord(request, user);
            
            // Convert VND to USD (rough conversion for demo)
            long amountInCents = convertVNDToUSDCents(request.getAmount());
            
            // Create Stripe PaymentIntent
            PaymentIntentCreateParams params = PaymentIntentCreateParams.builder()
                .setAmount(amountInCents)
                .setCurrency(currency)
                .setDescription(request.getDescription())
                .putMetadata("order_id", payment.getOrderId())
                .putMetadata("user_id", user.getId().toString())
                .putMetadata("membership_id", request.getMembershipId().toString())
                .setConfirmationMethod(PaymentIntentCreateParams.ConfirmationMethod.AUTOMATIC)
                .build();
            
            PaymentIntent intent = PaymentIntent.create(params);
            
            // Update payment with Stripe data
            payment.setGatewayTransactionId(intent.getId());
            payment.setGatewayResponseCode("created");
            payment.setGatewayMessage("Payment intent created");
            paymentRepository.save(payment);
            
            log.info("Stripe PaymentIntent created: {} for payment: {}", intent.getId(), payment.getId());
            
            return Map.of(
                "paymentId", payment.getId(),
                "clientSecret", intent.getClientSecret(),
                "paymentIntentId", intent.getId(),
                "orderId", payment.getOrderId(),
                "amount", payment.getAmount(),
                "amountUSD", amountInCents / 100.0,
                "currency", currency,
                "status", "requires_payment_method",
                "gateway", "STRIPE",
                "publishableKey", stripePublishableKey
            );
            
        } catch (StripeException e) {
            log.error("Stripe error creating PaymentIntent", e);
            throw new RuntimeException("Failed to create Stripe payment: " + e.getMessage());
        } catch (Exception e) {
            log.error("Error creating Stripe payment", e);
            throw new RuntimeException("Failed to create Stripe payment: " + e.getMessage());
        }
    }

    // =====================================================
    // CHECKOUT SESSION CREATION (Hosted checkout)
    // =====================================================

    public Map<String, Object> createCheckoutSession(PaymentRequest request, HttpServletRequest httpRequest) {
        try {
            // Get current user
            String username = SecurityContextHolder.getContext().getAuthentication().getName();
            User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("User not found"));
            
            // Create payment record
            Payment payment = createPaymentRecord(request, user);
            
            // Convert VND to USD
            long amountInCents = convertVNDToUSDCents(request.getAmount());
            
            // Create Stripe Checkout Session
            SessionCreateParams params = SessionCreateParams.builder()
                .setMode(SessionCreateParams.Mode.PAYMENT)
                .setSuccessUrl(successUrl + "?session_id={CHECKOUT_SESSION_ID}")
                .setCancelUrl(cancelUrl + "?order_id=" + payment.getOrderId())
                .addLineItem(
                    SessionCreateParams.LineItem.builder()
                        .setQuantity(1L)
                        .setPriceData(
                            SessionCreateParams.LineItem.PriceData.builder()
                                .setCurrency(currency)
                                .setUnitAmount(amountInCents)
                                .setProductData(
                                    SessionCreateParams.LineItem.PriceData.ProductData.builder()
                                        .setName("Real Estate Membership")
                                        .setDescription(request.getDescription())
                                        .build()
                                )
                                .build()
                        )
                        .build()
                )
                .putMetadata("order_id", payment.getOrderId())
                .putMetadata("user_id", user.getId().toString())
                .putMetadata("membership_id", request.getMembershipId().toString())
                .putMetadata("payment_id", payment.getId().toString())
                .setCustomerEmail(user.getEmail())
                .build();
            
            Session session = Session.create(params);
            
            // Update payment with session data
            payment.setGatewayTransactionId(session.getId());
            payment.setGatewayResponseCode("session_created");
            payment.setGatewayMessage("Checkout session created");
            paymentRepository.save(payment);
            
            log.info("Stripe Checkout Session created: {} for payment: {}", session.getId(), payment.getId());
            
            return Map.of(
                "paymentId", payment.getId(),
                "sessionId", session.getId(),
                "checkoutUrl", session.getUrl(),
                "orderId", payment.getOrderId(),
                "amount", payment.getAmount(),
                "amountUSD", amountInCents / 100.0,
                "currency", currency,
                "status", "pending",
                "gateway", "STRIPE",
                "expires", System.currentTimeMillis() + (30 * 60 * 1000) // 30 minutes
            );
            
        } catch (StripeException e) {
            log.error("Stripe error creating Checkout Session", e);
            throw new RuntimeException("Failed to create Stripe checkout: " + e.getMessage());
        } catch (Exception e) {
            log.error("Error creating Stripe checkout", e);
            throw new RuntimeException("Failed to create Stripe checkout: " + e.getMessage());
        }
    }

    // =====================================================
    // WEBHOOK PROCESSING
    // =====================================================

    public PaymentResponse processWebhook(String payload, String sigHeader, HttpServletRequest request) {
        try {
            // Verify webhook signature
            Event event = Webhook.constructEvent(payload, sigHeader, webhookSecret);
            
            log.info("Stripe webhook received: {}", event.getType());
            
            switch (event.getType()) {
                case "payment_intent.succeeded":
                    return handlePaymentIntentSucceeded(event);
                case "checkout.session.completed":
                    return handleCheckoutSessionCompleted(event);
                case "payment_intent.payment_failed":
                    return handlePaymentFailed(event);
                default:
                    log.info("Unhandled Stripe event type: {}", event.getType());
                    return null;
            }
            
        } catch (Exception e) {
            log.error("Error processing Stripe webhook", e);
            throw new RuntimeException("Failed to process Stripe webhook: " + e.getMessage());
        }
    }

    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================

    private Payment createPaymentRecord(PaymentRequest request, User user) {
        Payment payment = new Payment();
        payment.setUser(user);
        payment.setMembershipId(request.getMembershipId());
        payment.setAmount(request.getAmount());
        payment.setPaymentMethod(Payment.PaymentMethod.STRIPE);
        payment.setStatus(Payment.PaymentStatus.PENDING);
        payment.setDescription(request.getDescription());
        payment.setOrderId(generateOrderId());
        payment.setCreatedAt(LocalDateTime.now());
        payment.setUpdatedAt(LocalDateTime.now());
        
        return paymentRepository.save(payment);
    }

    private PaymentResponse handlePaymentIntentSucceeded(Event event) {
        PaymentIntent paymentIntent = (PaymentIntent) event.getDataObjectDeserializer()
            .getObject().orElse(null);
        
        if (paymentIntent == null) return null;
        
        String orderId = paymentIntent.getMetadata().get("order_id");
        Payment payment = paymentRepository.findByOrderId(orderId)
            .orElseThrow(() -> new RuntimeException("Payment not found: " + orderId));
        
        // Update payment status
        payment.setStatus(Payment.PaymentStatus.COMPLETED);
        payment.setGatewayResponseCode("succeeded");
        payment.setGatewayMessage("Payment succeeded");
        payment.setPaymentDate(LocalDateTime.now());
        payment.setCompletedAt(LocalDateTime.now());
        payment.setUpdatedAt(LocalDateTime.now());
        
        payment = paymentRepository.save(payment);
        
        // Process successful payment
        paymentService.processSuccessfulPayment(payment);
        
        log.info("Stripe payment succeeded for payment: {}", payment.getId());
        
        return paymentService.mapToPaymentResponse(payment);
    }

    private PaymentResponse handleCheckoutSessionCompleted(Event event) {
        Session session = (Session) event.getDataObjectDeserializer()
            .getObject().orElse(null);
        
        if (session == null) return null;
        
        String orderId = session.getMetadata().get("order_id");
        Payment payment = paymentRepository.findByOrderId(orderId)
            .orElseThrow(() -> new RuntimeException("Payment not found: " + orderId));
        
        // Update payment status
        payment.setStatus(Payment.PaymentStatus.COMPLETED);
        payment.setGatewayResponseCode("completed");
        payment.setGatewayMessage("Checkout session completed");
        payment.setPaymentDate(LocalDateTime.now());
        payment.setCompletedAt(LocalDateTime.now());
        payment.setUpdatedAt(LocalDateTime.now());
        
        payment = paymentRepository.save(payment);
        
        // Process successful payment
        paymentService.processSuccessfulPayment(payment);
        
        log.info("Stripe checkout completed for payment: {}", payment.getId());
        
        return paymentService.mapToPaymentResponse(payment);
    }

    private PaymentResponse handlePaymentFailed(Event event) {
        PaymentIntent paymentIntent = (PaymentIntent) event.getDataObjectDeserializer()
            .getObject().orElse(null);
        
        if (paymentIntent == null) return null;
        
        String orderId = paymentIntent.getMetadata().get("order_id");
        Payment payment = paymentRepository.findByOrderId(orderId)
            .orElseThrow(() -> new RuntimeException("Payment not found: " + orderId));
        
        // Update payment status
        payment.setStatus(Payment.PaymentStatus.FAILED);
        payment.setGatewayResponseCode("failed");
        payment.setGatewayMessage("Payment failed");
        payment.setFailureReason(paymentIntent.getLastPaymentError() != null ? 
            paymentIntent.getLastPaymentError().getMessage() : "Payment failed");
        payment.setUpdatedAt(LocalDateTime.now());
        
        payment = paymentRepository.save(payment);
        
        log.info("Stripe payment failed for payment: {}", payment.getId());
        
        return paymentService.mapToPaymentResponse(payment);
    }

    private long convertVNDToUSDCents(BigDecimal vndAmount) {
        // Rough conversion: 1 USD = 24,000 VND
        // For demo purposes - in production, use real exchange rates
        BigDecimal usdAmount = vndAmount.divide(new BigDecimal("24000"), 2, RoundingMode.HALF_UP);
        return usdAmount.multiply(new BigDecimal("100")).longValue(); // Convert to cents
    }

    private String generateOrderId() {
        return "STRIPE" + System.currentTimeMillis();
    }
} 