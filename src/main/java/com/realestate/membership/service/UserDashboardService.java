package com.realestate.membership.service;

import com.realestate.membership.entity.Property;
import com.realestate.membership.entity.UserMembership;
import com.realestate.membership.entity.UserMonthlyUsage;
import com.realestate.membership.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
@Slf4j
public class UserDashboardService {
    
    private final PropertyRepository propertyRepository;
    private final PropertyBoostRepository propertyBoostRepository;
    private final UserMonthlyUsageRepository userMonthlyUsageRepository;
    private final MembershipService membershipService;
    private final PaymentRepository paymentRepository;
    
    public Map<String, Object> getUserDashboard(Long userId) {
        Map<String, Object> dashboard = new HashMap<>();
        
        // Property statistics
        dashboard.put("properties", getPropertyStats(userId));
        
        // Membership information
        dashboard.put("membership", getMembershipInfo(userId));
        
        // Monthly usage
        dashboard.put("monthlyUsage", getMonthlyUsage(userId));
        
        // Recent activities
        dashboard.put("recentActivities", getRecentActivities(userId));
        
        // Suggestions
        dashboard.put("suggestions", getUpgradeSuggestions(userId));
        
        return dashboard;
    }
    
    private Map<String, Object> getPropertyStats(Long userId) {
        Map<String, Object> stats = new HashMap<>();
        
        // Basic counts
        stats.put("totalProperties", propertyRepository.countByUserId(userId));
        stats.put("approvedProperties", propertyRepository.countByUserIdAndStatus(userId, Property.PropertyStatus.APPROVED));
        stats.put("pendingProperties", propertyRepository.countByUserIdAndStatus(userId, Property.PropertyStatus.PENDING));
        stats.put("rejectedProperties", propertyRepository.countByUserIdAndStatus(userId, Property.PropertyStatus.REJECTED));
        
        // View statistics
        List<Property> userProperties = propertyRepository.findByUserIdAndStatus(userId, Property.PropertyStatus.APPROVED);
        long totalViews = userProperties.stream().mapToLong(Property::getViewCount).sum();
        long totalContacts = userProperties.stream().mapToLong(Property::getContactCount).sum();
        
        stats.put("totalViews", totalViews);
        stats.put("totalContacts", totalContacts);
        stats.put("averageViews", userProperties.isEmpty() ? 0 : totalViews / userProperties.size());
        
        // Active boosts
        List<com.realestate.membership.entity.PropertyBoost> activeBoosts = 
            propertyBoostRepository.findActiveBoostsByUserId(userId, LocalDateTime.now());
        stats.put("activeBoostedProperties", activeBoosts.size());
        
        return stats;
    }
    
    private Map<String, Object> getMembershipInfo(Long userId) {
        Map<String, Object> info = new HashMap<>();
        
        Optional<UserMembership> activeMembership = membershipService.getUserActiveMembership(userId);
        
        if (activeMembership.isPresent()) {
            UserMembership membership = activeMembership.get();
            info.put("hasActiveMembership", true);
            info.put("planName", membership.getMembership().getName());
            info.put("planType", membership.getMembership().getType());
            info.put("endDate", membership.getEndDate());
            info.put("daysRemaining", java.time.temporal.ChronoUnit.DAYS.between(LocalDateTime.now(), membership.getEndDate()));
            info.put("maxProperties", membership.getMembership().getMaxProperties());
            info.put("propertiesUsed", membership.getPropertiesUsed());
            info.put("propertiesRemaining", membership.getMembership().getMaxProperties() - membership.getPropertiesUsed());
            info.put("hasAiGeneration", membership.getMembership().getAiContentGeneration());
            info.put("pushTopLimit", membership.getMembership().getPushTopLimit());
            info.put("autoRenewal", membership.getAutoRenewal());
        } else {
            info.put("hasActiveMembership", false);
            info.put("message", "No active membership found");
        }
        
        return info;
    }
    
    private Map<String, Object> getMonthlyUsage(Long userId) {
        Map<String, Object> usage = new HashMap<>();
        
        String currentMonth = YearMonth.now().toString();
        Optional<UserMonthlyUsage> monthlyUsage = 
            userMonthlyUsageRepository.findByUserIdAndYearMonth(userId, currentMonth);
        
        if (monthlyUsage.isPresent()) {
            UserMonthlyUsage u = monthlyUsage.get();
            usage.put("pushTopUsed", u.getPushTopUsed());
            usage.put("aiContentUsed", u.getAiContentUsed());
        } else {
            usage.put("pushTopUsed", 0);
            usage.put("aiContentUsed", 0);
        }
        
        // Add limits from membership
        Optional<UserMembership> activeMembership = membershipService.getUserActiveMembership(userId);
        if (activeMembership.isPresent()) {
            usage.put("pushTopLimit", activeMembership.get().getMembership().getPushTopLimit());
            usage.put("pushTopRemaining", 
                activeMembership.get().getMembership().getPushTopLimit() - (Integer) usage.get("pushTopUsed"));
        }
        
        usage.put("month", currentMonth);
        usage.put("resetDate", YearMonth.now().plusMonths(1).atDay(1));
        
        return usage;
    }
    
    private List<Map<String, Object>> getRecentActivities(Long userId) {
        // Get recent property activities, boosts, etc.
        // This is a simplified version - can be enhanced
        return List.of(
            Map.of(
                "type", "property_created",
                "message", "Bài đăng mới được tạo",
                "timestamp", LocalDateTime.now().minusDays(1)
            ),
            Map.of(
                "type", "property_approved",
                "message", "Bài đăng được duyệt",
                "timestamp", LocalDateTime.now().minusDays(2)
            )
        );
    }
    
    private List<Map<String, Object>> getUpgradeSuggestions(Long userId) {
        List<Map<String, Object>> suggestions = new java.util.ArrayList<>();
        
        Optional<UserMembership> activeMembership = membershipService.getUserActiveMembership(userId);
        
        if (activeMembership.isEmpty()) {
            suggestions.add(Map.of(
                "type", "membership_required",
                "title", "Đăng ký gói thành viên",
                "message", "Đăng ký gói thành viên để đăng bài và sử dụng các tính năng cao cấp",
                "action", "upgrade",
                "priority", "high"
            ));
            return suggestions;
        }
        
        UserMembership membership = activeMembership.get();
        
        // Check if membership is expiring soon
        long daysRemaining = java.time.temporal.ChronoUnit.DAYS.between(LocalDateTime.now(), membership.getEndDate());
        if (daysRemaining <= 7) {
            suggestions.add(Map.of(
                "type", "membership_expiring",
                "title", "Gia hạn gói thành viên",
                "message", String.format("Gói thành viên sẽ hết hạn trong %d ngày", daysRemaining),
                "action", "renew",
                "priority", "high"
            ));
        }
        
        // Check if user is on BASIC plan and has high usage
        if (membership.getMembership().getType().toString().equals("BASIC")) {
            int propertiesUsed = membership.getPropertiesUsed();
            int maxProperties = membership.getMembership().getMaxProperties();
            
            if (propertiesUsed >= maxProperties * 0.8) {
                suggestions.add(Map.of(
                    "type", "upgrade_plan",
                    "title", "Nâng cấp lên gói Advanced",
                    "message", "Bạn đã sử dụng gần hết số lượng bài đăng. Nâng cấp để có thêm tính năng Push Top và AI Generation",
                    "action", "upgrade",
                    "priority", "medium"
                ));
            }
        }
        
        return suggestions;
    }
    
    public Map<String, Object> getPropertySEOScore(Long userId, Long propertyId) {
        // Verify property ownership
        Property property = propertyRepository.findById(propertyId)
                .orElseThrow(() -> new RuntimeException("Property not found"));
        
        if (!property.getUser().getId().equals(userId)) {
            throw new RuntimeException("You don't have permission to view this property");
        }
        
        // Calculate SEO score
        int seoScore = calculateSEOScore(property);
        
        return Map.of(
            "propertyId", propertyId,
            "seoScore", seoScore,
            "recommendations", getSEORecommendations(property, seoScore)
        );
    }
    
    private int calculateSEOScore(Property property) {
        int score = 0;
        
        // Title quality (20 points)
        if (property.getTitle() != null && property.getTitle().length() >= 30 && property.getTitle().length() <= 60) {
            score += 20;
        } else if (property.getTitle() != null && property.getTitle().length() >= 20) {
            score += 10;
        }
        
        // Description quality (30 points)
        if (property.getDescription() != null) {
            int descLength = property.getDescription().length();
            if (descLength >= 300 && descLength <= 1000) {
                score += 30;
            } else if (descLength >= 150) {
                score += 15;
            }
        }
        
        // Location details (20 points)
        if (property.getCity() != null && property.getDistrict() != null && property.getWard() != null) {
            score += 20;
        }
        
        // Property details (20 points)
        if (property.getPropertyArea() != null && property.getBedrooms() != null && property.getBathrooms() != null) {
            score += 20;
        }
        
        // Images (10 points)
        if (property.getImages() != null && !property.getImages().isEmpty()) {
            score += 10;
        }
        
        return Math.min(score, 100);
    }
    
    private List<String> getSEORecommendations(Property property, int seoScore) {
        List<String> recommendations = new java.util.ArrayList<>();
        
        if (seoScore < 80) {
            if (property.getTitle() == null || property.getTitle().length() < 30) {
                recommendations.add("Cải thiện tiêu đề: Sử dụng tiêu đề dài 30-60 ký tự với từ khóa quan trọng");
            }
            
            if (property.getDescription() == null || property.getDescription().length() < 300) {
                recommendations.add("Mở rộng mô tả: Viết mô tả chi tiết 300-1000 ký tự");
            }
            
            if (property.getImages() == null || property.getImages().isEmpty()) {
                recommendations.add("Thêm hình ảnh: Upload ít nhất 3-5 hình ảnh chất lượng cao");
            }
            
            recommendations.add("Sử dụng AI Content Generation để tối ưu SEO tự động");
        }
        
        return recommendations;
    }
}
