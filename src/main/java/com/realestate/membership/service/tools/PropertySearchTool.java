package com.realestate.membership.service.tools;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.realestate.membership.entity.Property;
import com.realestate.membership.service.PropertyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor
public class PropertySearchTool {
    
    private final PropertyService propertyService;
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * Tool definition for OpenAI function calling
     */
    public Map<String, Object> getToolDefinition() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        
        // District parameter
        Map<String, Object> district = new HashMap<>();
        district.put("type", "string");
        district.put("description", "Quận/huyện cần tìm (ví dụ: 'Quận 1', 'Quận 7', 'Thủ Đức')");
        properties.put("district", district);
        
        // Property type parameter
        Map<String, Object> propertyType = new HashMap<>();
        propertyType.put("type", "string");
        propertyType.put("enum", List.of("APARTMENT", "HOUSE", "VILLA", "LAND"));
        propertyType.put("description", "Loại bất động sản: APARTMENT (căn hộ), HOUSE (nhà phố), VILLA (biệt thự), LAND (đất)");
        properties.put("propertyType", propertyType);
        
        // Bedrooms parameter
        Map<String, Object> bedrooms = new HashMap<>();
        bedrooms.put("type", "integer");
        bedrooms.put("description", "Số phòng ngủ");
        properties.put("bedrooms", bedrooms);
        
        // Min price parameter
        Map<String, Object> minPrice = new HashMap<>();
        minPrice.put("type", "number");
        minPrice.put("description", "Giá tối thiểu (VNĐ)");
        properties.put("minPrice", minPrice);
        
        // Max price parameter
        Map<String, Object> maxPrice = new HashMap<>();
        maxPrice.put("type", "number");
        maxPrice.put("description", "Giá tối đa (VNĐ). Ví dụ: 5 tỷ = 5000000000");
        properties.put("maxPrice", maxPrice);
        
        parameters.put("properties", properties);
        
        return parameters;
    }
    
    /**
     * Execute the property search tool - Enhanced version
     */
    public PropertySearchResult executeSearchEnhanced(String functionArgs) {
        try {
            log.info("Executing enhanced property search with args: {}", functionArgs);
            
            // Parse function arguments
            JsonNode args = objectMapper.readTree(functionArgs);
            
            String district = args.has("district") ? args.get("district").asText() : null;
            String propertyTypeStr = args.has("propertyType") ? args.get("propertyType").asText() : null;
            Integer bedrooms = args.has("bedrooms") ? args.get("bedrooms").asInt() : null;
            BigDecimal minPrice = args.has("minPrice") ? new BigDecimal(args.get("minPrice").asText()) : null;
            BigDecimal maxPrice = args.has("maxPrice") ? new BigDecimal(args.get("maxPrice").asText()) : null;
            
            Property.PropertyType propertyType = null;
            if (propertyTypeStr != null) {
                try {
                    propertyType = Property.PropertyType.valueOf(propertyTypeStr);
                } catch (IllegalArgumentException e) {
                    log.warn("Invalid property type: {}", propertyTypeStr);
                }
            }
            
            // Search properties using repository
            List<Property> properties = propertyService.searchPropertiesByTool(
                district, propertyType, bedrooms, minPrice, maxPrice);
            
            if (properties.isEmpty()) {
                return new PropertySearchResult(
                    "Không tìm thấy bất động sản nào phù hợp với tiêu chí tìm kiếm.",
                    List.of()
                );
            }
            
            // Convert to PropertyResultItem with IDs
            List<PropertyResultItem> propertyItems = properties.stream()
                .limit(10) // Limit to 10 results for better UX
                .map(this::convertToPropertyResultItem)
                .collect(Collectors.toList());
            
            // Format results for AI response
            StringBuilder result = new StringBuilder();
            result.append(String.format("Tìm thấy %d bất động sản phù hợp:\\n\\n", properties.size()));
            
            int count = 0;
            for (PropertyResultItem item : propertyItems) {
                if (count >= 5) break; // Show only top 5 in text
                count++;
                
                result.append(String.format(
                    "%d. **%s**\\n" +
                    "   📍 Địa chỉ: %s, %s, %s\\n" +
                    "   💰 Giá: %,.0f VNĐ (%.1f tỷ)\\n" +
                    "   📐 Diện tích: %.0f m²\\n" +
                    "   🏠 Phòng ngủ: %d | Phòng tắm: %d\\n" +
                    "   🏷️ Loại: %s\\n" +
                    "   🔗 ID: %d\\n\\n",
                    count,
                    item.getTitle(),
                    item.getAddress(),
                    item.getWard(),
                    item.getDistrict(),
                    item.getPrice().doubleValue(),
                    item.getPrice().doubleValue() / 1000000000.0,
                    item.getArea() != null ? item.getArea().doubleValue() : 0,
                    item.getBedrooms() != null ? item.getBedrooms() : 0,
                    item.getBathrooms() != null ? item.getBathrooms() : 0,
                    item.getPropertyTypeVietnamese(),
                    item.getId()
                ));
            }
            
            if (properties.size() > 5) {
                result.append(String.format("\\n... và %d bất động sản khác. ", properties.size() - 5));
            }
            
            return new PropertySearchResult(result.toString(), propertyItems);
            
        } catch (Exception e) {
            log.error("Error executing enhanced property search tool", e);
            return new PropertySearchResult(
                "Đã xảy ra lỗi khi tìm kiếm bất động sản. Vui lòng thử lại.",
                List.of()
            );
        }
    }
    
    /**
     * Legacy method for backward compatibility
     */
    public String executeSearch(String functionArgs) {
        return executeSearchEnhanced(functionArgs).getTextResponse();
    }
    
    private PropertyResultItem convertToPropertyResultItem(Property property) {
        return PropertyResultItem.builder()
            .id(property.getId())
            .title(property.getTitle())
            .address(property.getAddress())
            .ward(property.getWard())
            .district(property.getDistrict())
            .city(property.getCity())
            .price(property.getPrice())
            .area(property.getPropertyArea())
            .bedrooms(property.getBedrooms())
            .bathrooms(property.getBathrooms())
            .propertyType(property.getPropertyType())
            .listingType(property.getListingType())
            .latitude(property.getLatitude())
            .longitude(property.getLongitude())
            .viewCount(property.getViewCount())
            .isFeatured(property.getIsFeatured())
            .build();
    }
    
    /**
     * Execute the property search tool - Legacy
     */
    public String executeSearchLegacy(String functionArgs) {
        try {
            log.info("Executing property search with args: {}", functionArgs);
            
            // Parse function arguments
            JsonNode args = objectMapper.readTree(functionArgs);
            
            String district = args.has("district") ? args.get("district").asText() : null;
            String propertyTypeStr = args.has("propertyType") ? args.get("propertyType").asText() : null;
            Integer bedrooms = args.has("bedrooms") ? args.get("bedrooms").asInt() : null;
            BigDecimal minPrice = args.has("minPrice") ? new BigDecimal(args.get("minPrice").asText()) : null;
            BigDecimal maxPrice = args.has("maxPrice") ? new BigDecimal(args.get("maxPrice").asText()) : null;
            
            Property.PropertyType propertyType = null;
            if (propertyTypeStr != null) {
                try {
                    propertyType = Property.PropertyType.valueOf(propertyTypeStr);
                } catch (IllegalArgumentException e) {
                    log.warn("Invalid property type: {}", propertyTypeStr);
                }
            }
            
            // Search properties using repository
            List<Property> properties = propertyService.searchPropertiesByTool(
                district, propertyType, bedrooms, minPrice, maxPrice);
            
            if (properties.isEmpty()) {
                return "Không tìm thấy bất động sản nào phù hợp với tiêu chí tìm kiếm.";
            }
            
            // Format results for AI response
            StringBuilder result = new StringBuilder();
            result.append(String.format("Tìm thấy %d bất động sản phù hợp:\\n\\n", properties.size()));
            
            int count = 0;
            for (Property property : properties) {
                if (count >= 5) break; // Limit to 5 results
                count++;
                
                result.append(String.format(
                    "%d. **%s**\\n" +
                    "   📍 Địa chỉ: %s, %s, %s\\n" +
                    "   💰 Giá: %,.0f VNĐ (%.1f tỷ)\\n" +
                    "   📐 Diện tích: %.0f m²\\n" +
                    "   🏠 Phòng ngủ: %d | Phòng tắm: %d\\n" +
                    "   🏷️ Loại: %s\\n\\n",
                    count,
                    property.getTitle(),
                    property.getAddress(),
                    property.getWard(),
                    property.getDistrict(),
                    property.getPrice().doubleValue(),
                    property.getPrice().doubleValue() / 1000000000.0,
                    property.getPropertyArea() != null ? property.getPropertyArea().doubleValue() : 0,
                    property.getBedrooms() != null ? property.getBedrooms() : 0,
                    property.getBathrooms() != null ? property.getBathrooms() : 0,
                    getPropertyTypeVietnamese(property.getPropertyType())
                ));
            }
            
            if (properties.size() > 5) {
                result.append(String.format("\\n... và %d bất động sản khác. ", properties.size() - 5));
            }
            
            return result.toString();
            
        } catch (Exception e) {
            log.error("Error executing property search tool", e);
            return "Đã xảy ra lỗi khi tìm kiếm bất động sản. Vui lòng thử lại.";
        }
    }
    
    private String getPropertyTypeVietnamese(Property.PropertyType type) {
        if (type == null) return "";
        switch (type) {
            case APARTMENT: return "Căn hộ";
            case HOUSE: return "Nhà phố";
            case VILLA: return "Biệt thự";
            case LAND: return "Đất";
            case COMMERCIAL: return "Thương mại";
            case OFFICE: return "Văn phòng";
            default: return type.toString();
        }
    }
    
    // Inner classes for structured response
    public static class PropertySearchResult {
        private final String textResponse;
        private final List<PropertyResultItem> properties;
        
        public PropertySearchResult(String textResponse, List<PropertyResultItem> properties) {
            this.textResponse = textResponse;
            this.properties = properties;
        }
        
        public String getTextResponse() { return textResponse; }
        public List<PropertyResultItem> getProperties() { return properties; }
    }
    
    @lombok.Builder
    @lombok.Data
    public static class PropertyResultItem {
        private Long id;
        private String title;
        private String address;
        private String ward;
        private String district;
        private String city;
        private BigDecimal price;
        private BigDecimal area;
        private Integer bedrooms;
        private Integer bathrooms;
        private Property.PropertyType propertyType;
        private Property.ListingType listingType;
        private BigDecimal latitude;
        private BigDecimal longitude;
        private Long viewCount;
        private Boolean isFeatured;
        
        public String getPropertyTypeVietnamese() {
            if (propertyType == null) return "";
            switch (propertyType) {
                case APARTMENT: return "Căn hộ";
                case HOUSE: return "Nhà phố";
                case VILLA: return "Biệt thự";
                case LAND: return "Đất";
                case COMMERCIAL: return "Thương mại";
                case OFFICE: return "Văn phòng";
                default: return propertyType.toString();
            }
        }
        
        public String getListingTypeVietnamese() {
            if (listingType == null) return "";
            return listingType == Property.ListingType.SALE ? "Bán" : "Cho thuê";
        }
    }
}
