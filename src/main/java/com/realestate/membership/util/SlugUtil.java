package com.realestate.membership.util;

import java.text.Normalizer;
import java.util.UUID;
import java.util.regex.Pattern;

public class SlugUtil {
    
    private static final Pattern NONLATIN = Pattern.compile("[^\\w-]");
    private static final Pattern WHITESPACE = Pattern.compile("[\\s]");
    private static final Pattern EDGESDHASHES = Pattern.compile("(^-|-$)");
    
    public static String generateSlug(String input) {
        if (input == null || input.trim().isEmpty()) {
            return UUID.randomUUID().toString();
        }
        
        String nowhitespace = WHITESPACE.matcher(input).replaceAll("-");
        String normalized = Normalizer.normalize(nowhitespace, Normalizer.Form.NFD);
        String slug = NONLATIN.matcher(normalized).replaceAll("");
        slug = EDGESDHASHES.matcher(slug).replaceAll("");
        slug = slug.toLowerCase();
        
        // Add random suffix to ensure uniqueness
        String suffix = UUID.randomUUID().toString().substring(0, 8);
        return slug.isEmpty() ? suffix : slug + "-" + suffix;
    }
    
    public static String generatePropertySlug(String title) {
        return generateSlug(title);
    }
    
    // Vietnamese character replacement
    public static String removeVietnameseAccents(String str) {
        if (str == null) return null;
        
        str = str.replaceAll("à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ", "a");
        str = str.replaceAll("è|é|ệ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ", "e");
        str = str.replaceAll("ì|í|ị|ỉ|ĩ", "i");
        str = str.replaceAll("ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ", "o");
        str = str.replaceAll("ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ", "u");
        str = str.replaceAll("ỳ|ý|ỵ|ỷ|ỹ", "y");
        str = str.replaceAll("đ", "d");
        
        str = str.replaceAll("À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ", "A");
        str = str.replaceAll("È|É|Ệ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ", "E");
        str = str.replaceAll("Ì|Í|Ị|Ỉ|Ĩ", "I");
        str = str.replaceAll("Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ", "O");
        str = str.replaceAll("Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ", "U");
        str = str.replaceAll("Ỳ|Ý|Ỵ|Ỷ|Ỹ", "Y");
        str = str.replaceAll("Đ", "D");
        
        return str;
    }
    
    public static String generateVietnameseSlug(String input) {
        if (input == null || input.trim().isEmpty()) {
            return UUID.randomUUID().toString();
        }
        
        // Remove Vietnamese accents
        String processed = removeVietnameseAccents(input);
        
        // Replace spaces with hyphens
        processed = processed.replaceAll("\\s+", "-");
        
        // Remove special characters except hyphens
        processed = processed.replaceAll("[^a-zA-Z0-9-]", "");
        
        // Remove multiple consecutive hyphens
        processed = processed.replaceAll("-+", "-");
        
        // Remove leading/trailing hyphens
        processed = processed.replaceAll("^-|-$", "");
        
        // Convert to lowercase
        processed = processed.toLowerCase();
        
        // Add random suffix
        String suffix = UUID.randomUUID().toString().substring(0, 8);
        return processed.isEmpty() ? suffix : processed + "-" + suffix;
    }
}
