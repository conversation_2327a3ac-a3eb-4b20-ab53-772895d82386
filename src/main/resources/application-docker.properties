# =====================================================
# DOCKER ENVIRONMENT CONFIGURATION
# Spring Boot profile for containerized deployment
# =====================================================

# Docker-specific configurations
# Remove spring.profiles.active line since this is already a profile-specific resource

# ===== DATABASE CONFIGURATION =====
spring.datasource.url=${SPRING_DATASOURCE_URL}
spring.datasource.username=${SPRING_DATASOURCE_USERNAME}
spring.datasource.password=${SPRING_DATASOURCE_PASSWORD}
spring.datasource.driver-class-name=org.postgresql.Driver

# Connection Pool Settings for Docker
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.connection-timeout=20000

# ===== JPA/HIBERNATE CONFIGURATION =====
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.open-in-view=false

# ===== SERVER CONFIGURATION =====
server.port=${SERVER_PORT}
server.servlet.context-path=/api/v1
server.compression.enabled=true

# ===== REDIS CONFIGURATION =====
spring.redis.host=${SPRING_REDIS_HOST}
spring.redis.port=${SPRING_REDIS_PORT}
spring.redis.timeout=2000ms
spring.cache.type=redis

# ===== OAUTH2 GOOGLE CONFIGURATION =====
spring.security.oauth2.client.registration.google.client-id=${SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GOOGLE_CLIENT_ID}
spring.security.oauth2.client.registration.google.client-secret=${SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GOOGLE_CLIENT_SECRET}
spring.security.oauth2.client.registration.google.scope=openid,email,profile
spring.security.oauth2.client.registration.google.redirect-uri={baseUrl}/login/oauth2/code/google
spring.security.oauth2.client.registration.google.authorization-grant-type=authorization_code

# ===== JWT CONFIGURATION =====
jwt.secret=${JWT_SECRET}
jwt.expiration=${JWT_EXPIRATION}
app.jwt.secret=${JWT_SECRET}
app.jwt.expiration=${JWT_EXPIRATION}

# ===== OPENAI API CONFIGURATION =====
openai.api.key=${OPENAI_API_KEY}
openai.api.base-url=https://api.openai.com/v1
openai.api.model=gpt-4o
openai.api.max-tokens=2000
openai.api.temperature=0.3
openai.api.timeout=45

# ===== CHATBOT CONFIGURATION =====
chatbot.enabled=true
chatbot.max-history=10
chatbot.response-timeout=30000
chatbot.language=vi

# ===== FILE UPLOAD CONFIGURATION =====
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
app.upload.dir=/app/uploads/
app.upload.allowed-types=jpg,jpeg,png,gif,pdf,doc,docx

# ===== MAIL CONFIGURATION =====
spring.mail.host=${MAIL_HOST}
spring.mail.port=${MAIL_PORT}
spring.mail.username=${MAIL_USERNAME}
spring.mail.password=${MAIL_PASSWORD}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# ===== LOGGING CONFIGURATION =====
logging.level.com.realestate.membership=INFO
logging.level.org.springframework.security=INFO
logging.level.org.hibernate=INFO
logging.file.name=/app/logs/application.log
logging.logback.rollingpolicy.max-file-size=10MB
logging.logback.rollingpolicy.total-size-cap=100MB

# ===== ACTUATOR CONFIGURATION =====
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=when_authorized
management.metrics.export.prometheus.enabled=true

# ===== CORS CONFIGURATION =====
app.cors.allowed-origins=${FRONTEND_URL}
app.cors.allowed-methods=GET,POST,PUT,DELETE,PATCH,OPTIONS
app.cors.allowed-headers=*
app.cors.allow-credentials=true

# ===== APPLICATION CONFIGURATION =====
app.default.page.size=10
app.max.page.size=100
app.frontend.url=${FRONTEND_URL}

# ===== AWS S3 CONFIGURATION =====
aws.s3.region=${AWS_S3_REGION}
aws.s3.bucket-name=${AWS_S3_BUCKET_NAME}
aws.s3.access-key=${AWS_S3_ACCESS_KEY}
aws.s3.secret-key=${AWS_S3_SECRET_KEY}
aws.s3.endpoint=${AWS_S3_ENDPOINT}
aws.s3.cdn-domain=${AWS_S3_CDN_DOMAIN}

# ===== SWAGGER/OPENAPI CONFIGURATION =====
springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui/index.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.try-it-out-enabled=true
springdoc.swagger-ui.filter=true
springdoc.show-actuator=true
springdoc.packages-to-scan=com.realestate.membership.controller
springdoc.paths-to-match=/** 