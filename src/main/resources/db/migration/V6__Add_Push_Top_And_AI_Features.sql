-- =====================================================
-- V6: Add Push Top and AI Content Generation Features
-- =====================================================

-- Add new columns to memberships table
ALTER TABLE memberships 
ADD COLUMN ai_content_generation BOOLEAN DEFAULT FALSE,
ADD COLUMN push_top_limit INTEGER DEFAULT 0;

-- Update existing membership types to only BASIC and ADVANCED
UPDATE memberships SET type = 'BASIC' WHERE type IN ('FREE', 'BASIC');
UPDATE memberships SET type = 'ADVANCED' WHERE type IN ('PREMIUM', 'VIP');

-- Update membership features for new structure
UPDATE memberships SET 
    ai_content_generation = FALSE,
    push_top_limit = 0,
    max_properties = 10,
    featured_properties = 0,
    price = 99000,
    description = 'G<PERSON><PERSON> cơ bản cho người dùng cá nhân - Đăng bài thông thường'
WHERE type = 'BASIC';

UPDATE memberships SET 
    ai_content_generation = TRUE,
    push_top_limit = 10,
    max_properties = 50,
    featured_properties = 10,
    price = 299000,
    description = 'Gói nâng cao với AI Generation và Push Top (10 lượt/tháng)'
WHERE type = 'ADVANCED';

-- Create property_boosts table
CREATE TABLE property_boosts (
    id BIGSERIAL PRIMARY KEY,
    property_id BIGINT NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    boost_type VARCHAR(50) NOT NULL DEFAULT 'PUSH_TOP',
    boost_start TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    boost_end TIMESTAMP NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for property_boosts
CREATE INDEX idx_property_boosts_property_id ON property_boosts(property_id);
CREATE INDEX idx_property_boosts_user_id ON property_boosts(user_id);
CREATE INDEX idx_property_boosts_active ON property_boosts(is_active, boost_start, boost_end);
CREATE INDEX idx_property_boosts_type_active ON property_boosts(boost_type, is_active, boost_start, boost_end);

-- Create user_monthly_usage table
CREATE TABLE user_monthly_usage (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    year_month VARCHAR(7) NOT NULL, -- Format: "2024-12"
    push_top_used INTEGER NOT NULL DEFAULT 0,
    ai_content_used INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, year_month)
);

-- Create indexes for user_monthly_usage
CREATE INDEX idx_user_monthly_usage_user_month ON user_monthly_usage(user_id, year_month);
CREATE INDEX idx_user_monthly_usage_year_month ON user_monthly_usage(year_month);

-- Add trigger to update updated_at columns
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers
CREATE TRIGGER update_property_boosts_updated_at 
    BEFORE UPDATE ON property_boosts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_monthly_usage_updated_at 
    BEFORE UPDATE ON user_monthly_usage 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Remove old membership types that are no longer used
DELETE FROM memberships WHERE type NOT IN ('BASIC', 'ADVANCED');

-- Insert default memberships if they don't exist
INSERT INTO memberships (name, type, price, duration_months, max_properties, featured_properties, 
                        priority_support, analytics_access, multiple_images, contact_info_display,
                        ai_content_generation, push_top_limit, is_active, description, created_at, updated_at)
SELECT 'Basic', 'BASIC', 99000, 1, 10, 0, FALSE, FALSE, TRUE, TRUE, FALSE, 0, TRUE,
       'Gói cơ bản cho người dùng cá nhân - Đăng bài thông thường', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
WHERE NOT EXISTS (SELECT 1 FROM memberships WHERE type = 'BASIC');

INSERT INTO memberships (name, type, price, duration_months, max_properties, featured_properties, 
                        priority_support, analytics_access, multiple_images, contact_info_display,
                        ai_content_generation, push_top_limit, is_active, description, created_at, updated_at)
SELECT 'Advanced', 'ADVANCED', 299000, 1, 50, 10, TRUE, TRUE, TRUE, TRUE, TRUE, 10, TRUE,
       'Gói nâng cao với AI Generation và Push Top (10 lượt/tháng)', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
WHERE NOT EXISTS (SELECT 1 FROM memberships WHERE type = 'ADVANCED');

-- Add comments for documentation
COMMENT ON TABLE property_boosts IS 'Tracks property boost/push-top activities';
COMMENT ON TABLE user_monthly_usage IS 'Tracks monthly usage limits for push-top and AI features';
COMMENT ON COLUMN memberships.ai_content_generation IS 'Whether this membership includes AI content generation';
COMMENT ON COLUMN memberships.push_top_limit IS 'Monthly limit for push-top feature (0 = not available)';
