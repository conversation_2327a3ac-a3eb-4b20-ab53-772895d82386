package com.realestate.membership.config;

import com.realestate.membership.service.OpenAIService;
import com.realestate.membership.service.tools.PropertySearchTool;
import com.realestate.membership.service.S3Service;
import org.mockito.Mockito;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;

@TestConfiguration
@Profile("test")
public class TestConfig {

    @Bean
    @Primary
    public OpenAIService mockOpenAIService() {
        return Mockito.mock(OpenAIService.class);
    }
    
    @Bean
    @Primary
    public PropertySearchTool mockPropertySearchTool() {
        return Mockito.mock(PropertySearchTool.class);
    }
    
    // Note: S3Service should be mocked in individual tests where needed
    // to allow different behavior testing
}
