package com.realestate.membership.controller;

import com.realestate.membership.entity.Property;
import com.realestate.membership.entity.User;
import com.realestate.membership.service.PropertyService;
import com.realestate.membership.service.S3Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(PropertyController.class)
class PropertyControllerUploadTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PropertyService propertyService;

    @MockBean
    private S3Service s3Service;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @WithMockUser(roles = "USER")
    void uploadPropertyImages_Success() throws Exception {
        // Given
        UUID propertyId = UUID.randomUUID();
        MockMultipartFile image1 = new MockMultipartFile(
            "images", 
            "house1.jpg", 
            "image/jpeg", 
            "image1 content".getBytes()
        );
        MockMultipartFile image2 = new MockMultipartFile(
            "images", 
            "house2.jpg", 
            "image/jpeg", 
            "image2 content".getBytes()
        );

        List<String> uploadedUrls = Arrays.asList(
            "https://bucket.s3.amazonaws.com/properties/house1.jpg",
            "https://bucket.s3.amazonaws.com/properties/house2.jpg"
        );

        when(s3Service.uploadFile(any(), eq("properties")))
            .thenReturn("https://bucket.s3.amazonaws.com/properties/house1.jpg")
            .thenReturn("https://bucket.s3.amazonaws.com/properties/house2.jpg");

        when(propertyService.addImages(any(UUID.class), any(UUID.class), anyList()))
            .thenReturn(uploadedUrls);

        // When & Then
        mockMvc.perform(multipart("/api/v1/properties/{id}/images", propertyId)
                .file(image1)
                .file(image2)
                .with(csrf())
                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0]").value(uploadedUrls.get(0)))
                .andExpect(jsonPath("$[1]").value(uploadedUrls.get(1)));
    }

    @Test
    @WithMockUser(roles = "USER")
    void uploadPropertyImages_InvalidFileType() throws Exception {
        // Given
        UUID propertyId = UUID.randomUUID();
        MockMultipartFile invalidFile = new MockMultipartFile(
            "images", 
            "document.pdf", 
            "application/pdf", 
            "pdf content".getBytes()
        );

        when(s3Service.uploadFile(any(), any()))
            .thenThrow(new RuntimeException("Invalid file type"));

        // When & Then
        mockMvc.perform(multipart("/api/v1/properties/{id}/images", propertyId)
                .file(invalidFile)
                .with(csrf())
                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser(roles = "USER")
    void uploadPropertyImages_EmptyFiles() throws Exception {
        // Given
        UUID propertyId = UUID.randomUUID();

        // When & Then
        mockMvc.perform(multipart("/api/v1/properties/{id}/images", propertyId)
                .with(csrf())
                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser(roles = "USER")
    void deletePropertyImage_Success() throws Exception {
        // Given
        UUID propertyId = UUID.randomUUID();
        String imageUrl = "https://bucket.s3.amazonaws.com/properties/house1.jpg";

        // When & Then
        mockMvc.perform(delete("/api/v1/properties/{id}/images", propertyId)
                .param("imageUrl", imageUrl)
                .with(csrf()))
                .andExpect(status().isNoContent());
    }

    @Test
    void uploadPropertyImages_Unauthorized() throws Exception {
        // Given
        UUID propertyId = UUID.randomUUID();
        MockMultipartFile image = new MockMultipartFile(
            "images", 
            "house.jpg", 
            "image/jpeg", 
            "image content".getBytes()
        );

        // When & Then
        mockMvc.perform(multipart("/api/v1/properties/{id}/images", propertyId)
                .file(image)
                .with(csrf())
                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(roles = "USER")
    void uploadPropertyImages_FileTooLarge() throws Exception {
        // Given
        UUID propertyId = UUID.randomUUID();
        byte[] largeContent = new byte[11 * 1024 * 1024]; // 11MB
        MockMultipartFile largeFile = new MockMultipartFile(
            "images", 
            "large.jpg", 
            "image/jpeg", 
            largeContent
        );

        when(s3Service.uploadFile(any(), any()))
            .thenThrow(new RuntimeException("File too large"));

        // When & Then
        mockMvc.perform(multipart("/api/v1/properties/{id}/images", propertyId)
                .file(largeFile)
                .with(csrf())
                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isBadRequest());
    }
}
