package com.realestate.membership.service;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.PutObjectResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.net.URL;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class S3ServiceTest {

    @Mock
    private AmazonS3 amazonS3Client;

    private S3Service s3Service;
    private final String bucketName = "test-bucket";
    private final String cdnDomain = "cdn.example.com";

    @BeforeEach
    void setUp() throws Exception {
        s3Service = new S3Service(amazonS3Client);
        ReflectionTestUtils.setField(s3Service, "bucketName", bucketName);
        ReflectionTestUtils.setField(s3Service, "cdnDomain", cdnDomain);
    }

    @Test
    void uploadFile_Success_WithCDN() throws IOException {
        // Given
        MockMultipartFile file = new MockMultipartFile(
            "image", 
            "test.jpg", 
            "image/jpeg", 
            "test image content".getBytes()
        );
        
        PutObjectResult mockResult = new PutObjectResult();
        when(amazonS3Client.putObject(any(PutObjectRequest.class)))
            .thenReturn(mockResult);

        // When
        String result = s3Service.uploadFile(file, "properties");

        // Then
        assertNotNull(result);
        assertTrue(result.startsWith("https://" + cdnDomain + "/"));
        assertTrue(result.contains("properties/"));
        assertTrue(result.endsWith(".jpg"));
        verify(amazonS3Client).putObject(any(PutObjectRequest.class));
    }

    @Test
    void uploadFile_Success_WithoutCDN() throws IOException {
        // Given
        ReflectionTestUtils.setField(s3Service, "cdnDomain", "");
        
        MockMultipartFile file = new MockMultipartFile(
            "image", 
            "test.jpg", 
            "image/jpeg", 
            "test image content".getBytes()
        );
        
        PutObjectResult mockResult = new PutObjectResult();
        URL mockUrl = new URL("https://" + bucketName + ".s3.amazonaws.com/properties/test.jpg");
        
        when(amazonS3Client.putObject(any(PutObjectRequest.class)))
            .thenReturn(mockResult);
        when(amazonS3Client.getUrl(anyString(), anyString()))
            .thenReturn(mockUrl);

        // When
        String result = s3Service.uploadFile(file, "properties");

        // Then
        assertNotNull(result);
        assertTrue(result.contains(bucketName));
        assertTrue(result.contains("properties/"));
        assertTrue(result.endsWith(".jpg"));
        verify(amazonS3Client).putObject(any(PutObjectRequest.class));
        verify(amazonS3Client).getUrl(eq(bucketName), anyString());
    }

    @Test
    void uploadFile_NullFilename() throws IOException {
        // Given
        MockMultipartFile file = new MockMultipartFile(
            "image", 
            null, // null filename
            "image/jpeg", 
            "test image content".getBytes()
        );
        
        PutObjectResult mockResult = new PutObjectResult();
        when(amazonS3Client.putObject(any(PutObjectRequest.class)))
            .thenReturn(mockResult);

        // When
        String result = s3Service.uploadFile(file, "properties");

        // Then
        assertNotNull(result);
        assertTrue(result.startsWith("https://" + cdnDomain + "/"));
        assertTrue(result.contains("properties/"));
        verify(amazonS3Client).putObject(any(PutObjectRequest.class));
    }

    @Test
    void uploadFile_FilenameWithoutExtension() throws IOException {
        // Given
        MockMultipartFile file = new MockMultipartFile(
            "image", 
            "testfile", // no extension
            "image/jpeg", 
            "test image content".getBytes()
        );
        
        PutObjectResult mockResult = new PutObjectResult();
        when(amazonS3Client.putObject(any(PutObjectRequest.class)))
            .thenReturn(mockResult);

        // When
        String result = s3Service.uploadFile(file, "properties");

        // Then
        assertNotNull(result);
        assertTrue(result.startsWith("https://" + cdnDomain + "/"));
        assertTrue(result.contains("properties/"));
        // Should end with just a dot since no extension
        assertTrue(result.endsWith("."));
        verify(amazonS3Client).putObject(any(PutObjectRequest.class));
    }

    @Test
    void deleteFile_Success_WithCDN() {
        // Given
        String fileUrl = "https://" + cdnDomain + "/properties/test-file.jpg";

        // When
        s3Service.deleteFile(fileUrl);

        // Then
        verify(amazonS3Client).deleteObject(eq(bucketName), eq("properties/test-file.jpg"));
    }

    @Test
    void deleteFile_Success_WithS3URL() {
        // Given
        ReflectionTestUtils.setField(s3Service, "cdnDomain", "");
        String fileUrl = "https://" + bucketName + ".s3.amazonaws.com/properties/test-file.jpg";

        // When
        s3Service.deleteFile(fileUrl);

        // Then
        verify(amazonS3Client).deleteObject(eq(bucketName), eq("properties/test-file.jpg"));
    }

    @Test
    void deleteFile_Success_WithComplexPath() {
        // Given
        String fileUrl = "https://" + cdnDomain + "/users/profile-pics/uuid-123/avatar.png";

        // When
        s3Service.deleteFile(fileUrl);

        // Then
        verify(amazonS3Client).deleteObject(eq(bucketName), eq("users/profile-pics/uuid-123/avatar.png"));
    }
}
