package com.realestate.membership.util;

import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockMultipartFile;

import static org.junit.jupiter.api.Assertions.*;

class ImageValidationUtilTest {

    @Test
    void isValidImageFile_ValidExtensions() {
        assertTrue(ImageValidationUtil.isValidImageFile("image.jpg"));
        assertTrue(ImageValidationUtil.isValidImageFile("photo.jpeg"));
        assertTrue(ImageValidationUtil.isValidImageFile("picture.png"));
        assertTrue(ImageValidationUtil.isValidImageFile("animation.gif"));
        assertTrue(ImageValidationUtil.isValidImageFile("modern.webp"));
        
        // Case insensitive
        assertTrue(ImageValidationUtil.isValidImageFile("IMAGE.JPG"));
        assertTrue(ImageValidationUtil.isValidImageFile("Photo.JPEG"));
    }

    @Test
    void isValidImageFile_InvalidExtensions() {
        assertFalse(ImageValidationUtil.isValidImageFile("document.pdf"));
        assertFalse(ImageValidationUtil.isValidImageFile("video.mp4"));
        assertFalse(ImageValidationUtil.isValidImageFile("audio.mp3"));
        assertFalse(ImageValidationUtil.isValidImageFile("file.txt"));
        assertFalse(ImageValidationUtil.isValidImageFile("executable.exe"));
        assertFalse(ImageValidationUtil.isValidImageFile("archive.zip"));
    }

    @Test
    void isValidImageFile_EdgeCases() {
        assertFalse(ImageValidationUtil.isValidImageFile(null));
        assertFalse(ImageValidationUtil.isValidImageFile(""));
        assertFalse(ImageValidationUtil.isValidImageFile("noextension"));
        assertFalse(ImageValidationUtil.isValidImageFile(".jpg")); // No filename
        assertFalse(ImageValidationUtil.isValidImageFile("file.")); // No extension
    }

    @Test
    void isValidFileSize_ValidSizes() {
        byte[] smallFile = new byte[1024]; // 1KB
        byte[] mediumFile = new byte[5 * 1024 * 1024]; // 5MB
        byte[] maxFile = new byte[10 * 1024 * 1024]; // 10MB (max allowed)

        MockMultipartFile small = new MockMultipartFile("file", "small.jpg", "image/jpeg", smallFile);
        MockMultipartFile medium = new MockMultipartFile("file", "medium.jpg", "image/jpeg", mediumFile);
        MockMultipartFile max = new MockMultipartFile("file", "max.jpg", "image/jpeg", maxFile);

        assertTrue(ImageValidationUtil.isValidFileSize(small));
        assertTrue(ImageValidationUtil.isValidFileSize(medium));
        assertTrue(ImageValidationUtil.isValidFileSize(max));
    }

    @Test
    void isValidFileSize_InvalidSizes() {
        byte[] emptyFile = new byte[0];
        byte[] largeFile = new byte[11 * 1024 * 1024]; // 11MB (too large)

        MockMultipartFile empty = new MockMultipartFile("file", "empty.jpg", "image/jpeg", emptyFile);
        MockMultipartFile large = new MockMultipartFile("file", "large.jpg", "image/jpeg", largeFile);

        assertFalse(ImageValidationUtil.isValidFileSize(empty));
        assertFalse(ImageValidationUtil.isValidFileSize(large));
    }

    @Test
    void isValidContentType_ValidTypes() {
        MockMultipartFile jpegFile = new MockMultipartFile("file", "test.jpg", "image/jpeg", "content".getBytes());
        MockMultipartFile pngFile = new MockMultipartFile("file", "test.png", "image/png", "content".getBytes());
        MockMultipartFile gifFile = new MockMultipartFile("file", "test.gif", "image/gif", "content".getBytes());
        MockMultipartFile webpFile = new MockMultipartFile("file", "test.webp", "image/webp", "content".getBytes());

        assertTrue(ImageValidationUtil.isValidContentType(jpegFile));
        assertTrue(ImageValidationUtil.isValidContentType(pngFile));
        assertTrue(ImageValidationUtil.isValidContentType(gifFile));
        assertTrue(ImageValidationUtil.isValidContentType(webpFile));
    }

    @Test
    void isValidContentType_InvalidTypes() {
        MockMultipartFile pdfFile = new MockMultipartFile("file", "test.pdf", "application/pdf", "content".getBytes());
        MockMultipartFile textFile = new MockMultipartFile("file", "test.txt", "text/plain", "content".getBytes());
        MockMultipartFile videoFile = new MockMultipartFile("file", "test.mp4", "video/mp4", "content".getBytes());

        assertFalse(ImageValidationUtil.isValidContentType(pdfFile));
        assertFalse(ImageValidationUtil.isValidContentType(textFile));
        assertFalse(ImageValidationUtil.isValidContentType(videoFile));
    }

    @Test
    void validateImageFile_AllValid() {
        byte[] validContent = new byte[1024 * 1024]; // 1MB
        MockMultipartFile validFile = new MockMultipartFile(
            "file", "house.jpg", "image/jpeg", validContent
        );

        assertDoesNotThrow(() -> {
            ImageValidationUtil.validateImageFile(validFile);
        });
    }

    @Test
    void validateImageFile_InvalidExtension() {
        byte[] content = new byte[1024];
        MockMultipartFile invalidFile = new MockMultipartFile(
            "file", "document.pdf", "image/jpeg", content
        );

        assertThrows(IllegalArgumentException.class, () -> {
            ImageValidationUtil.validateImageFile(invalidFile);
        });
    }

    @Test
    void validateImageFile_InvalidSize() {
        byte[] largeContent = new byte[11 * 1024 * 1024]; // 11MB
        MockMultipartFile largeFile = new MockMultipartFile(
            "file", "large.jpg", "image/jpeg", largeContent
        );

        assertThrows(IllegalArgumentException.class, () -> {
            ImageValidationUtil.validateImageFile(largeFile);
        });
    }

    @Test
    void validateImageFile_InvalidContentType() {
        byte[] content = new byte[1024];
        MockMultipartFile invalidFile = new MockMultipartFile(
            "file", "fake.jpg", "text/plain", content
        );

        assertThrows(IllegalArgumentException.class, () -> {
            ImageValidationUtil.validateImageFile(invalidFile);
        });
    }

    @Test
    void generateUniqueFileName_PreservesExtension() {
        String originalName = "my house photo.jpg";
        String uniqueName = ImageValidationUtil.generateUniqueFileName(originalName);
        
        assertNotNull(uniqueName);
        assertTrue(uniqueName.endsWith(".jpg"));
        assertNotEquals(originalName, uniqueName);
        
        // Should contain timestamp and nanotime
        assertTrue(uniqueName.matches("\\d+_\\d+_.*\\.jpg"));
    }

    @Test
    void generateUniqueFileName_HandlesSpecialCharacters() {
        String originalName = "my-house_photo (1).JPG";
        String uniqueName = ImageValidationUtil.generateUniqueFileName(originalName);
        
        assertNotNull(uniqueName);
        assertTrue(uniqueName.endsWith(".JPG"));
        // Should sanitize special characters
        assertFalse(uniqueName.contains("("));
        assertFalse(uniqueName.contains(")"));
    }

    @Test
    void generateUniqueFileName_MultipleCalls() throws InterruptedException {
        String originalName = "test.jpg";
        String name1 = ImageValidationUtil.generateUniqueFileName(originalName);
        Thread.sleep(1); // Ensure different timestamps
        String name2 = ImageValidationUtil.generateUniqueFileName(originalName);
        
        assertNotEquals(name1, name2); // Should be unique
    }
}
