# Test Configuration for EC2/Production Environment
# This configuration is optimized for running tests in EC2

# ===== DATABASE CONFIGURATION (H2 In-Memory for Tests) =====
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.h2.console.enabled=false

# ===== JPA/HIBERNATE CONFIGURATION =====
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.defer-datasource-initialization=true
spring.jpa.generate-ddl=true

# ===== DISABLE EXTERNAL SERVICES =====
spring.sql.init.mode=never

# ===== JWT CONFIGURATION =====
jwt.secret=test-secret-key-for-testing-purposes-only
jwt.expiration=86400000

# ===== OPENAI CONFIGURATION (DISABLED FOR TESTS) =====
openai.api.key=test-key
openai.api.model=gpt-3.5-turbo
openai.api.base-url=https://api.openai.com/v1

# ===== PAYMENT GATEWAYS (DISABLED FOR TESTS) =====
vnpay.enabled=false
momo.enabled=false
stripe.enabled=false

# ===== EMAIL CONFIGURATION (DISABLED FOR TESTS) =====
spring.mail.host=localhost
spring.mail.port=25
spring.mail.username=test
spring.mail.password=test

# ===== LOGGING CONFIGURATION =====
logging.level.com.realestate.membership=WARN
logging.level.org.springframework.security=WARN
logging.level.org.hibernate=WARN
logging.level.org.springframework.web=WARN
logging.level.org.springframework.test=WARN

# ===== DISABLE SCHEDULED TASKS =====
spring.task.scheduling.enabled=false

# ===== DISABLE ADMIN INITIALIZATION =====
app.admin.auto-create=false

# ===== FILE UPLOAD CONFIGURATION =====
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
app.upload.dir=/tmp/test-uploads
app.upload.max-images-per-property=10

# ===== AWS S3 CONFIGURATION (MOCKED) =====
aws.s3.region=ap-southeast-2
aws.s3.bucket-name=test-bucket
aws.s3.access-key=test-key
aws.s3.secret-key=test-secret
aws.s3.cdn-domain=

# ===== DISABLE OAUTH FOR TESTS =====
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.oauth2.client.servlet.OAuth2ClientAutoConfiguration

# ===== CORS CONFIGURATION =====
app.cors.allowed-origins=http://localhost:3000
app.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
app.cors.allowed-headers=*
app.cors.allow-credentials=true

# ===== FRONTEND URL =====
app.frontend.url=http://localhost:3000
