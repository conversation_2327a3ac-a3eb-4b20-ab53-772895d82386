# Test configuration
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# JWT config for tests
app.jwt.secret=testSecretKey123456789012345678901234567890
app.jwt.expiration=86400000

# Upload config for tests
app.upload.max-images-per-property=10

# Disable OAuth for tests
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.oauth2.client.servlet.OAuth2ClientAutoConfiguration

# Test AWS S3 config (will be mocked)
aws.s3.region=ap-southeast-2
aws.s3.bucket-name=test-bucket
aws.s3.access-key=test-key
aws.s3.secret-key=test-secret
aws.s3.cdn-domain=

# Logging
logging.level.com.realestate.membership=DEBUG
logging.level.org.springframework.test=DEBUG
