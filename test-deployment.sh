#!/bin/bash

# =====================================================
# DEPLOYMENT TEST SCRIPT FOR EC2
# Test all services after deployment
# =====================================================

echo "🧪 Testing Real Estate Membership System Deployment"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test function
test_endpoint() {
    local url=$1
    local description=$2
    local expected_status=${3:-200}
    
    echo -n "Testing $description... "
    
    response=$(curl -s -o /dev/null -w "%{http_code}" "$url" --max-time 10)
    
    if [ "$response" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} (HTTP $response)"
        return 0
    else
        echo -e "${RED}❌ FAIL${NC} (HTTP $response)"
        return 1
    fi
}

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 30

# Test 1: Container Status
echo -e "\n📊 Container Status:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Test 2: Health Checks
echo -e "\n🏥 Health Checks:"

# App Health Check
test_endpoint "http://localhost:8080/api/v1/actuator/health" "Spring Boot Health"

# Nginx Health Check  
test_endpoint "http://localhost/health" "Nginx Health"

# Test 3: API Endpoints
echo -e "\n🌐 API Endpoints:"

# Swagger UI
test_endpoint "http://localhost:8080/api/v1/swagger-ui/index.html" "Swagger UI"

# API Documentation
test_endpoint "http://localhost:8080/api/v1/api-docs" "API Documentation"

# Test 4: Database Connectivity
echo -e "\n🗄️ Database Tests:"

# PostgreSQL Connection
echo -n "Testing PostgreSQL connection... "
if docker exec realestate-postgres pg_isready -U realestate_user -d realestate_membership >/dev/null 2>&1; then
    echo -e "${GREEN}✅ PASS${NC}"
else
    echo -e "${RED}❌ FAIL${NC}"
fi

# Redis Connection
echo -n "Testing Redis connection... "
if docker exec realestate-redis redis-cli ping >/dev/null 2>&1; then
    echo -e "${GREEN}✅ PASS${NC}"
else
    echo -e "${RED}❌ FAIL${NC}"
fi

# Test 5: Application Logs Check
echo -e "\n📝 Application Logs Check:"
echo "Last 10 lines of application logs:"
docker logs realestate-app --tail 10

# Test 6: Memory and CPU Usage
echo -e "\n💻 Resource Usage:"
docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"

# Test 7: Network Connectivity
echo -e "\n🌐 Network Tests:"

# Test internal network connectivity
echo -n "Testing app -> postgres connection... "
if docker exec realestate-app nc -z postgres 5432 >/dev/null 2>&1; then
    echo -e "${GREEN}✅ PASS${NC}"
else
    echo -e "${RED}❌ FAIL${NC}"
fi

echo -n "Testing app -> redis connection... "
if docker exec realestate-app nc -z redis 6379 >/dev/null 2>&1; then
    echo -e "${GREEN}✅ PASS${NC}"
else
    echo -e "${RED}❌ FAIL${NC}"
fi

echo -n "Testing nginx -> app connection... "
if docker exec realestate-nginx nc -z app 8080 >/dev/null 2>&1; then
    echo -e "${GREEN}✅ PASS${NC}"
else
    echo -e "${RED}❌ FAIL${NC}"
fi

# Summary
echo -e "\n📋 Test Summary:"
echo "================================"
echo "🌐 Application URL: http://$(curl -s ifconfig.me):8080/api/v1/swagger-ui/index.html"
echo "🔧 Health Check: http://$(curl -s ifconfig.me):8080/api/v1/actuator/health"
echo "📚 API Docs: http://$(curl -s ifconfig.me):8080/api/v1/api-docs"
echo "🌐 Nginx Proxy: http://$(curl -s ifconfig.me)/"

echo -e "\n✅ Deployment test completed!"
echo "If any tests failed, check the logs with: docker logs <container-name>"
