#!/bin/bash

# =====================================================
# TEST NEW FEATURES SCRIPT
# Test Push Top, AI Content, and Dashboard features
# =====================================================

echo "🧪 Testing New Real Estate Features..."
echo "====================================="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BASE_URL="http://localhost:8080/api/v1"
if [ "$1" != "" ]; then
    BASE_URL="$1/api/v1"
fi

echo -e "${BLUE}Testing against: $BASE_URL${NC}"

# Test function
test_endpoint() {
    local method=$1
    local endpoint=$2
    local description=$3
    local auth_header=$4
    local data=$5
    
    echo -n "Testing $description... "
    
    if [ "$method" = "GET" ]; then
        if [ -n "$auth_header" ]; then
            response=$(curl -s -w "%{http_code}" -H "$auth_header" "$BASE_URL$endpoint")
        else
            response=$(curl -s -w "%{http_code}" "$BASE_URL$endpoint")
        fi
    elif [ "$method" = "POST" ]; then
        if [ -n "$data" ]; then
            response=$(curl -s -w "%{http_code}" -X POST -H "Content-Type: application/json" -H "$auth_header" -d "$data" "$BASE_URL$endpoint")
        else
            response=$(curl -s -w "%{http_code}" -X POST -H "$auth_header" "$BASE_URL$endpoint")
        fi
    fi
    
    http_code="${response: -3}"
    body="${response%???}"
    
    if [ "$http_code" = "200" ] || [ "$http_code" = "201" ]; then
        echo -e "${GREEN}✅ PASS${NC} (HTTP $http_code)"
        return 0
    elif [ "$http_code" = "401" ]; then
        echo -e "${YELLOW}🔒 AUTH REQUIRED${NC} (HTTP $http_code)"
        return 1
    else
        echo -e "${RED}❌ FAIL${NC} (HTTP $http_code)"
        echo "Response: $body"
        return 1
    fi
}

# Test 1: Basic API Health
echo -e "\n📊 Basic API Tests:"
test_endpoint "GET" "/actuator/health" "Health Check"
test_endpoint "GET" "/memberships" "Get Memberships"
test_endpoint "GET" "/properties" "Get Properties"

# Test 2: Authentication Required Endpoints
echo -e "\n🔒 Authentication Tests:"
test_endpoint "GET" "/dashboard" "User Dashboard (No Auth)"
test_endpoint "GET" "/properties/boost/status" "Push Top Status (No Auth)"
test_endpoint "POST" "/ai-content/generate" "AI Content Generation (No Auth)"

# Test 3: Swagger Documentation
echo -e "\n📚 Documentation Tests:"
test_endpoint "GET" "/swagger-ui/index.html" "Swagger UI"
test_endpoint "GET" "/api-docs" "API Documentation"

# Test 4: Database Migration Check
echo -e "\n🗄️ Database Tests:"
echo -n "Checking database tables... "

# Simple check if we can connect to the database
if docker exec realestate-postgres psql -U realestate_user -d realestate_membership -c "\dt" >/dev/null 2>&1; then
    echo -e "${GREEN}✅ PASS${NC}"
    
    # Check if new tables exist
    echo -n "Checking property_boosts table... "
    if docker exec realestate-postgres psql -U realestate_user -d realestate_membership -c "\d property_boosts" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ EXISTS${NC}"
    else
        echo -e "${RED}❌ MISSING${NC}"
    fi
    
    echo -n "Checking user_monthly_usage table... "
    if docker exec realestate-postgres psql -U realestate_user -d realestate_membership -c "\d user_monthly_usage" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ EXISTS${NC}"
    else
        echo -e "${RED}❌ MISSING${NC}"
    fi
    
    echo -n "Checking membership types... "
    membership_types=$(docker exec realestate-postgres psql -U realestate_user -d realestate_membership -t -c "SELECT DISTINCT type FROM memberships;")
    if echo "$membership_types" | grep -q "BASIC" && echo "$membership_types" | grep -q "ADVANCED"; then
        echo -e "${GREEN}✅ CORRECT${NC} (BASIC, ADVANCED)"
    else
        echo -e "${YELLOW}⚠️ CHECK NEEDED${NC}"
        echo "Found types: $membership_types"
    fi
    
else
    echo -e "${RED}❌ FAIL${NC}"
fi

# Test 5: Application Logs Check
echo -e "\n📝 Application Logs Check:"
echo "Checking for errors in application logs..."
error_count=$(docker logs realestate-app --tail 100 2>&1 | grep -i error | wc -l)
if [ "$error_count" -eq 0 ]; then
    echo -e "${GREEN}✅ No errors found${NC}"
else
    echo -e "${YELLOW}⚠️ Found $error_count error(s)${NC}"
    echo "Recent errors:"
    docker logs realestate-app --tail 20 2>&1 | grep -i error | tail -5
fi

# Test 6: Feature Endpoints Structure
echo -e "\n🚀 New Feature Endpoints:"
echo "📍 Push Top Feature:"
echo "   POST $BASE_URL/properties/boost/{propertyId}/push-top"
echo "   GET  $BASE_URL/properties/boost/status"
echo "   GET  $BASE_URL/properties/boost/history"
echo "   GET  $BASE_URL/properties/boost/active"

echo -e "\n🤖 AI Content Feature:"
echo "   POST $BASE_URL/ai-content/generate"

echo -e "\n📊 Dashboard Feature:"
echo "   GET  $BASE_URL/dashboard"
echo "   GET  $BASE_URL/dashboard/seo/score/{propertyId}"

# Test 7: Membership Features Check
echo -e "\n💎 Membership Features:"
echo -n "Checking Basic membership features... "
basic_features=$(docker exec realestate-postgres psql -U realestate_user -d realestate_membership -t -c "SELECT ai_content_generation, push_top_limit FROM memberships WHERE type='BASIC';")
if echo "$basic_features" | grep -q "f.*0"; then
    echo -e "${GREEN}✅ CORRECT${NC} (No AI, No Push Top)"
else
    echo -e "${YELLOW}⚠️ CHECK NEEDED${NC}"
    echo "Basic features: $basic_features"
fi

echo -n "Checking Advanced membership features... "
advanced_features=$(docker exec realestate-postgres psql -U realestate_user -d realestate_membership -t -c "SELECT ai_content_generation, push_top_limit FROM memberships WHERE type='ADVANCED';")
if echo "$advanced_features" | grep -q "t.*10"; then
    echo -e "${GREEN}✅ CORRECT${NC} (AI + 10 Push Top)"
else
    echo -e "${YELLOW}⚠️ CHECK NEEDED${NC}"
    echo "Advanced features: $advanced_features"
fi

# Summary
echo -e "\n📋 Test Summary:"
echo "=================================="
echo -e "🌐 API Base URL: ${BLUE}$BASE_URL${NC}"
echo -e "📚 Swagger UI: ${BLUE}$BASE_URL/../swagger-ui/index.html${NC}"
echo -e "🔧 Health Check: ${BLUE}$BASE_URL/actuator/health${NC}"

echo -e "\n✨ New Features Available:"
echo -e "   🚀 Push Top (Advanced members only)"
echo -e "   🤖 AI Content Generation (Advanced members only)"
echo -e "   📊 User Dashboard"
echo -e "   📈 SEO Scoring"
echo -e "   💎 2-Tier Membership (Basic/Advanced)"

echo -e "\n🎯 Next Steps:"
echo "1. Create user account and login"
echo "2. Subscribe to Advanced membership"
echo "3. Create property and test Push Top"
echo "4. Test AI Content Generation"
echo "5. Check Dashboard analytics"

echo -e "\n✅ Feature testing completed!"
