#!/bin/bash

echo "🧪 COMPREHENSIVE API TESTING"
echo "=============================="

BASE_URL="http://localhost:8080/api/v1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to test API endpoint
test_api() {
    local method=$1
    local endpoint=$2
    local data=$3
    local headers=$4
    local description=$5
    
    echo -e "\n${BLUE}Testing: $description${NC}"
    echo "Endpoint: $method $endpoint"
    
    if [ -n "$data" ]; then
        if [ -n "$headers" ]; then
            response=$(curl -s -X $method "$BASE_URL$endpoint" -H "Content-Type: application/json" -H "$headers" -d "$data")
        else
            response=$(curl -s -X $method "$BASE_URL$endpoint" -H "Content-Type: application/json" -d "$data")
        fi
    else
        if [ -n "$headers" ]; then
            response=$(curl -s -X $method "$BASE_URL$endpoint" -H "Content-Type: application/json" -H "$headers")
        else
            response=$(curl -s -X $method "$BASE_URL$endpoint" -H "Content-Type: application/json")
        fi
    fi
    
    # Check if response is valid JSON
    if echo "$response" | jq . >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Success${NC}"
        echo "$response" | jq .
    else
        echo -e "${RED}❌ Failed or Invalid JSON${NC}"
        echo "Response: $response"
    fi
}

echo -e "\n${YELLOW}=== 1. ADMIN AUTHENTICATION ===${NC}"

# Admin Login
echo -e "\n${BLUE}🔐 Admin Login${NC}"
admin_response=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}')

if echo "$admin_response" | jq . >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Admin login successful${NC}"
    echo "$admin_response" | jq .
    ADMIN_TOKEN=$(echo "$admin_response" | jq -r '.token // .accessToken // .jwt // empty')
    if [ -n "$ADMIN_TOKEN" ] && [ "$ADMIN_TOKEN" != "null" ]; then
        echo -e "${GREEN}Admin token extracted: ${ADMIN_TOKEN:0:50}...${NC}"
    else
        echo -e "${RED}❌ Could not extract admin token${NC}"
        ADMIN_TOKEN=""
    fi
else
    echo -e "${RED}❌ Admin login failed${NC}"
    echo "Response: $admin_response"
    ADMIN_TOKEN=""
fi

echo -e "\n${YELLOW}=== 2. PUBLIC ENDPOINTS ===${NC}"

# Test public endpoints
test_api "GET" "/memberships" "" "" "Get all memberships"
test_api "GET" "/categories" "" "" "Get all categories"
test_api "GET" "/properties" "" "" "Get all properties"
test_api "GET" "/payments/methods" "" "" "Get payment methods"
test_api "GET" "/payments/exchange-rate" "" "" "Get exchange rate"

echo -e "\n${YELLOW}=== 3. USER REGISTRATION & LOGIN ===${NC}"

# Create test user
test_user_data='{"username":"testuser123","email":"<EMAIL>","password":"password123","firstName":"Test","lastName":"User"}'
test_api "POST" "/auth/register" "$test_user_data" "" "Register new user"

# User Login
echo -e "\n${BLUE}🔐 User Login${NC}"
user_response=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser123","password":"password123"}')

if echo "$user_response" | jq . >/dev/null 2>&1; then
    echo -e "${GREEN}✅ User login successful${NC}"
    echo "$user_response" | jq .
    USER_TOKEN=$(echo "$user_response" | jq -r '.token // .accessToken // .jwt // empty')
    if [ -n "$USER_TOKEN" ] && [ "$USER_TOKEN" != "null" ]; then
        echo -e "${GREEN}User token extracted: ${USER_TOKEN:0:50}...${NC}"
    else
        echo -e "${RED}❌ Could not extract user token${NC}"
        USER_TOKEN=""
    fi
else
    echo -e "${RED}❌ User login failed${NC}"
    echo "Response: $user_response"
    USER_TOKEN=""
fi

echo -e "\n${YELLOW}=== 4. USER AUTHENTICATED ENDPOINTS ===${NC}"

if [ -n "$USER_TOKEN" ]; then
    test_api "GET" "/notifications" "" "Authorization: Bearer $USER_TOKEN" "Get user notifications"
    test_api "GET" "/notifications/unread/count" "" "Authorization: Bearer $USER_TOKEN" "Get unread notifications count"
    test_api "GET" "/memberships/my-membership" "" "Authorization: Bearer $USER_TOKEN" "Get user membership"
    test_api "GET" "/oauth/user-info" "" "Authorization: Bearer $USER_TOKEN" "Get OAuth user info"
    
    # Test payment creation
    payment_data='{"membershipId":1,"paymentMethod":"VNPAY","amount":99000,"description":"Test payment","returnUrl":"http://localhost:3000/payment/result"}'
    test_api "POST" "/payments/create" "$payment_data" "Authorization: Bearer $USER_TOKEN" "Create payment"
    
    # Test chatbot session
    chat_data='{"title":"Test Chat Session"}'
    test_api "POST" "/chatbot/sessions" "$chat_data" "Authorization: Bearer $USER_TOKEN" "Create chat session"
else
    echo -e "${RED}❌ Skipping user authenticated tests - no valid token${NC}"
fi

echo -e "\n${YELLOW}=== 5. ADMIN AUTHENTICATED ENDPOINTS ===${NC}"

if [ -n "$ADMIN_TOKEN" ]; then
    test_api "GET" "/admin/users" "" "Authorization: Bearer $ADMIN_TOKEN" "Get all users (Admin)"
    test_api "GET" "/admin/dashboard/stats" "" "Authorization: Bearer $ADMIN_TOKEN" "Get dashboard stats (Admin)"
    
    # Test admin user management
    test_api "GET" "/admin/users?page=0&size=10" "" "Authorization: Bearer $ADMIN_TOKEN" "Get users with pagination (Admin)"
    
    # Test admin property management
    test_api "GET" "/admin/properties" "" "Authorization: Bearer $ADMIN_TOKEN" "Get all properties (Admin)"
    
    # Test admin membership management
    test_api "GET" "/admin/memberships" "" "Authorization: Bearer $ADMIN_TOKEN" "Get all memberships (Admin)"
    
    # Test admin notifications
    test_api "GET" "/admin/notifications" "" "Authorization: Bearer $ADMIN_TOKEN" "Get all notifications (Admin)"
    
    # Test admin payments
    test_api "GET" "/admin/payments" "" "Authorization: Bearer $ADMIN_TOKEN" "Get all payments (Admin)"
else
    echo -e "${RED}❌ Skipping admin authenticated tests - no valid token${NC}"
fi

echo -e "\n${YELLOW}=== 6. PROPERTY MANAGEMENT ===${NC}"

if [ -n "$USER_TOKEN" ]; then
    # Create property
    property_data='{
        "title":"Test Property",
        "description":"A beautiful test property",
        "price":1000000,
        "address":"123 Test Street",
        "city":"Ho Chi Minh City",
        "district":"District 1",
        "ward":"Ward 1",
        "propertyType":"APARTMENT",
        "listingType":"SALE",
        "bedrooms":2,
        "bathrooms":1,
        "propertyArea":80.5,
        "categoryId":1
    }'
    test_api "POST" "/properties" "$property_data" "Authorization: Bearer $USER_TOKEN" "Create property"
    
    # Get user properties
    test_api "GET" "/properties/my-properties" "" "Authorization: Bearer $USER_TOKEN" "Get user properties"
else
    echo -e "${RED}❌ Skipping property tests - no valid user token${NC}"
fi

echo -e "\n${YELLOW}=== 7. CATEGORY MANAGEMENT ===${NC}"

if [ -n "$ADMIN_TOKEN" ]; then
    # Create category
    category_data='{"name":"Test Category","description":"A test category","iconUrl":"https://example.com/icon.png","isActive":true,"sortOrder":1}'
    test_api "POST" "/admin/categories" "$category_data" "Authorization: Bearer $ADMIN_TOKEN" "Create category (Admin)"
else
    echo -e "${RED}❌ Skipping category tests - no valid admin token${NC}"
fi

echo -e "\n${YELLOW}=== 8. MEMBERSHIP MANAGEMENT ===${NC}"

if [ -n "$ADMIN_TOKEN" ]; then
    # Create membership
    membership_data='{
        "name":"Test Membership",
        "description":"A test membership plan",
        "type":"BASIC",
        "price":99000,
        "durationMonths":1,
        "maxProperties":10,
        "featuredProperties":2,
        "multipleImages":true,
        "contactInfoDisplay":true,
        "prioritySupport":false,
        "analyticsAccess":false,
        "isActive":true
    }'
    test_api "POST" "/admin/memberships" "$membership_data" "Authorization: Bearer $ADMIN_TOKEN" "Create membership (Admin)"
else
    echo -e "${RED}❌ Skipping membership tests - no valid admin token${NC}"
fi

echo -e "\n${GREEN}🎉 API Testing Complete!${NC}"
echo "=============================="
